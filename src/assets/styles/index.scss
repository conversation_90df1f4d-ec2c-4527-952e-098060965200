@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 10px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
    padding: 0 !important;
//   margin-top: 30px;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}
.form_row{
    height: 44px;
    overflow: hidden;
}
.form_box{
    margin-top: 0px !important;
    // padding: 16px !important;
    .el-card__body{
        padding: 16px !important;
    }
}
.el-card__body{
    padding: 10px !important;
}
.form_btn {
    .form_btn_col{
        display: flex;
        justify-content: flex-end;
    }
}
.el-card{
    margin-top: 0 !important;
}
.form_row .form_col .el-input{
    width: 215px;
}
.form_row .form_col .el-form-item{
    margin-bottom: 13px !important;
}
.form_box .el-form-item{
    margin-bottom: 0 !important;
}
// 列表状态样式
.round{
    display: flex;
    align-items: center;
    justify-content: center;
    &-dot{
        margin-right: 5px;
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background: #5672FA;
    }
}
.el-dialog__footer {
    text-align: center !important;
  }
//状态居中
.justifyC{
    justify-content: center;
}
//按钮颜色
.btn_color_f{
    color: #F85300 !important;
}
.btn_color_t{
    color: #12AE63  !important;
}
.btn_color_four{
    color: #8000FF !important;
    opacity: 0.9;
}
.btn_color_three{
    color: #4E5969 !important;
}
.drawer_box{
    .el-drawer__header{
        margin: 0;
        padding: 18px 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        .el-dialog__close{
            font-size: 22px;
            font-weight: 500;
        }
    }
    .el-drawer__body{
        padding: 0 20px 0 56px;
    }
    .el-input__inner {
        height: 30px;
    }
    .el-input__icon{
        line-height: normal;
    }
    .el-date-editor .el-range-separator{
        line-height: 20px;
    }
}

.ellipsis { 
  overflow: hidden; 
  text-overflow: ellipsis; 
  white-space: nowrap;
}