import request from "@/utils/request";
import { basicPath2 } from "@/api/base.js";
import { xmbBaseInfo } from "@/api/xmb/xmbCommon";

// 列表
export const cattleHealthList = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/onlineConsultation/listAll`,
    method: "post",
    data: data,
  });
};
export const cattleHealthDetail = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/onlineConsultation/selectById`,
    method: "post",
    data: data,
  });
};
export const cattleHealthReply = (data) => {
  data.appId = xmbBaseInfo.appId;
  return request({
    url: `${basicPath2}/onlineConsultation/reply`,
    method: "post",
    data: data,
  });
};

