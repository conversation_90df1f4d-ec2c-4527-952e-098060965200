import request from "@/utils/request"
/* 
    担保交易
*/

// const userinfo = JSON.parse(window.localStorage.getItem("USERINFO"))

// 查询担保交易列表
export const SecuredList = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/queryQuotaApply",
    method: "post",
    data: data
  })
}

// 查询额度记录
export const queryApplyDetail = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/queryApplyDetail",
    method: "post",
    data: data
  })
}

// 审核、取消、驳回
export const changeApproStatus = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/changeApproStatus",
    method: "post",
    data: data
  })
}

export const queryQuotaFeeList = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/queryQuotaFeeList",
    method: "post",
    data: data
  })
}
export const quotaFeeConf = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/quotaFeeConf",
    method: "post",
    data: data
  })
}

// 获取银行列表
export const bankList = (data) => {
  data.extsysCode = *********
  return request({
    url: "nyb-service/empayOperator/customerQueryPage",
    method: "post",
    data: data
  })
}

// 农赢保外部系统登录获取信息
export const extsysLogin = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/nlg/payFUser/extsysLogin",
    method: "post",
    data: data
  })
}

// 农赢保注册会员（未查询到会员信息）
export const extsysRegister = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/payExtsys/register",
    method: "post",
    data: data
  })
}

// 担保交易明细列表
export const quotaTransList = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/quotaTransList",
    method: "post",
    data: data
  })
}

// 导出担保交易明细
/* export const exportQuotaTrans = (name, code) => {
  // data.extsysCode = "*********"
  return request({
    url: `nyb-service/export/exportQuotaTrans?fileName=${name}&orderCodes=${code}&extsysCode=*********`,
    method: "get",
    data: {
      extsysCode: *********
    }
  })
} */
export const exportQuotaTrans = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/export/exportQuotaTrans",
    method: "post",
    data: data,
  })
}



// 担保交易明细导出记录
export const queryExportRecord = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/queryExportRecord",
    method: "post",
    data: data
  })
}

// 还款信息 已还列表
export const queryRepayment = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/queryRepayment",
    method: "post",
    data: data
  })
}

// 上传
export const uploadRepaymentRecords = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/uploadRepaymentRecords",
    method: "post",
    data: data
  })
}

// 还款 - 查询预存记录 - 对比
export const queryPreRepayment = (data) => {
  return request({
    url: "nyb-service/empayOperator/queryPreRepayment",
    method: "post",
    data: data
  })
}

// 取消上传
export const cancelUploadRepayment = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/cancelUploadRepayment",
    method: "post",
    data: data
  })
}

// 确认上传
export const confirmUploadRepayment = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/confirmUploadRepayment",
    method: "post",
    data: data
  })
}

// 推荐企业列表
export const payFQuotaGuarantorList = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/payFQuotaGuarantor/queryPage",
    method: "post",
    data: data
  })
}

// 字典接口
export const qryParamByType = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/qryParamByType",
    method: "post",
    data: data
  })
}
// 导出
export const exportQuotaRecord = (query) => {
  query.extsysCode = "*********"
  return request({
    url: "nyb-service/export/exportQuotaRecord",
    method: "get",
    responseType: 'blob',
    params: query
  })
}

// 字典接口
export const payFQuotaGuarantorQueryPage = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/payFQuotaGuarantor/queryPage",
    method: "post",
    data: data
  })
}
// 银行列表
export const selectBank = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/payFCustomer/selectBank",
    method: "post",
    data: data
  })
}
// 撤销
export const empayRollback = (data) => {
  data.extsysCode = "*********"
  return request({
    url: "nyb-service/empayOperator/rollback",
    method: "post",
    data: data
  })
}
