import request from "@/utils/request"

// 运营角色还款信息维护 新增单笔还款
export const addRepayment = (data) => {
  data.extsysCode = 100000010
  return request({
    url: "nyb-service/empayOperator/addRepayment",
    method: "post",
    data: data
  })
}

// 消费记录合同下载
export const exportContract = (data) => { 
  data.extsysCode = 100000010
 return request({
    url: "nyb-service/empayOperator/downloadContract",
    method: "post",
    data: data
 })
}

// 消费记录合同下载
export const uploadContract = (data) => { 
  data.extsysCode = 100000010
 return request({
    url: "nyb-service/empayOperator/uploadContract",
    method: "post",
    data: data
 })
}

// 购销合同下载
export const downloadContract = (data) => {
  data.extsysCode = 100000010;
  return request({
    url: "nyb-service/payExtsys/downloadContract",
    method: "post",
    data: data,
  });
};