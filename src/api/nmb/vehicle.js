import request from "@/utils/request";
import { basicPath10 } from "@/api/base.js";
/* 
    车辆管理
*/

// 列表
/**
 * 
 入参：{
  "carType": "大型车",
  "pageNum": 0,
  "pageSize": 0,
  "plateNumber": "京A12345",
  "status": "1 启用 2禁用",
  "tradingTenantId": 0
 }
  返回数据结构：
  {
	"code": 0,
	"message": "",
	"result": {
		"lastPage": true,
		"list": [
			{
				"carLength": 6.5, // 车长
				"carType": "大型车", // 车型： 1、大货车，2、中型车
				"cityCountyTenantId": 0,
				"createBy": "",
				"createTime": "",
				"delFlag": "",
				"endTime": "",
				"endTime2": "",
				"frontPhoto": "", // 车头照片
				"id": 1,
				"licenseFrontImage": "", // 行驶证证明
				"licenseRearImage": "", // 行驶证反面
				"loginNmbCompanyId": 0,
				"loginNmbUserId": 0,
				"loginUserId": 0,
				"nickName": "", // 车辆归属人名称
				"ownerId": 1001,
				"ownerIdCardFrontImage": "/images/idcard/front/123.jpg", // 身份证正面
				"ownerIdCardNumber": 110101199001011234, // 身份证号
				"ownerIdCardRearImage": "/images/idcard/rear/123.jpg", // 身份证反面
				"ownerPhone": 150111, // 车辆归属人手机号
				"pageNum": 0,
				"pageSize": 0,
				"params": {},
				"plateNumber": "京A12345", // 车牌号
				"platformId": 0,
				"rearPhoto": "", // 车尾照片
				"remark": "",
				"searchValue": "",
				"sortBy": "",
				"sortType": "",
				"startTime": "",
				"startTime2": "",
				"tenantId": 0,
				"tradingTenantId": 0,
				"updateBy": "",
				"updateTime": ""
			}
		],
		"pageNum": 0,
		"pageSize": 0,
		"pages": 0,
		"params": {},
		"size": 0,
		"total": 0
	},
	"success": true,
	"timestamp": 0
}
    参数描述：
    
 */
export const carPage = (data) => {
  return request({
    url: `${basicPath10}nmb/car/page`,
    method: "post",
    data: data,
  });
};

// 新增
/**
 * 
入参：按照原型的参数填写
 */
export const carAdd = (data) => {
  return request({
    url: `${basicPath10}nmb/car/add`,
    method: "post",
    data: data,
  });
};

// 编辑
/**
 * 
入参：按照原型的参数填写
需要编辑的数据id
 */
export const carEdit = (data) => {
  return request({
    url: `${basicPath10}nmb/car/edit`,
    method: "post",
    data: data,
  });
};

// 删除
/**
 *
入参：carIds，支持多个删除 ‘1，2，3，4’
 */
export const carDelete = (data) => {
  return request({
    url: `${basicPath10}nmb/car/delete`,
    method: "post",
    data: data,
  });
};

// 详情
export const carInfo = (data) => {
  return request({
    url: `${basicPath10}nmb/car/info`,
    method: "post",
    data: data,
  });
};

// 启用 get 请求，传入id参数
export const carEnable = (params) => {
  return request({
    url: `${basicPath10}nmb/car/enable`,
    method: "get",
    params: params,
  });
};

// 禁用， get请求，传入id参数
export const carDisable = (params) => {
  return request({
    url: `${basicPath10}nmb/car/disable`,
    method: "get",
    params: params,
  });
};
