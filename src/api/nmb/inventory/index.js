import request from "@/utils/request";
import { basicPath2, basicPath4, basicPath10 } from "@/api/base.js";

/**
 * * 养殖人员
 */

// 新增
export const breederAdd = (data) => {
  return request({
    url: `${basicPath2}pasture/breeder/add`,
    method: "post",
    data: data,
  });
};
// 编辑
export const breederEdit = (data) => {
  return request({
    url: `${basicPath2}pasture/breeder/edit`,
    method: "post",
    data: data,
  });
};

// 详情
export const breederInfo = (data) => {
  return request({
    url: `${basicPath2}pasture/breeder/info`,
    method: "post",
    data: data,
  });
};

// 列表
export const breederList = (data) => {
  return request({
    url: `${basicPath2}pasture/breeder/page`,
    method: "post",
    data: data,
  });
};
// 操作记录
export const actionRecordPage = (data) => {
  return request({
    url: `${basicPath2}pasture/breeder/actionRecord/page`,
    method: "post",
    data: data,
  });
};

/**
 * * 养殖场地
 */

// 牧场列表分页查询
export function pasturelist(data) {
  return request({
    url: `${basicPath2}pasture/manager/pageV2`,
    method: "post",
    data: data,
  });
}

// 货品管理列表
export function goodsLivestockListExport(params) {
  return request({
    url: `${basicPath2}excel/ncs/export/goodsLivestockList`,
    method: "post",
    data: params,
    responseType: "blob",
  });
}

//牧场新增
export function pastureAdd(params) {
  return request({
    url: basicPath2 + "pasture/addV2",
    method: "post",
    data: params,
  });
}

//牧场编辑
export function pastureEdit(params) {
  return request({
    url: basicPath2 + "pasture/editV2",
    method: "post",
    data: params,
  });
}

//查询牧场详情
export function pastureById(params) {
  return request({
    url: basicPath2 + "pasture/selectByIdV2",
    method: "post",
    data: params,
  });
}

/**
 * 活畜档案
 */
// 新增活畜 
export const addLivestock = (data) => {
  return request({
    url: `${basicPath2}pastureLivestock/addLivestockV2`,
    method: "post",
    data: data,
  });
};

// 活畜列表
export function pastureLivList(params) {
    return request({
        url: `${basicPath2}pastureLivestock/page`,
        method: 'post',
        data: params
    })
}
// 活畜列表
export function livestockList(data) {
  return request({
    url: `${basicPath2}livestock/livestock/list`,
    method: 'post',
    data: data
  })
}

// 活畜品类列表
export function animalTypeList(data) {
  return request({
    url: `${basicPath2}livestock/livestockCategory/list`,
    method: 'post',
    data: data
  })
}

// 活畜品种列表
export function varietiesList(data) {
  return request({
    url: `${basicPath2}livestock/livestockVarieties/list`,
    method: 'post',
    data: data
  })
}

// 绑定活畜

/**
 * 在养管理
 */

// 饲养管理

// 生长检测

// 疾病防控-免疫记录
export function immunePage(params) {
  return request({
    url: basicPath2 + "manage/immune/page",
    method: "post",
    data: params,
  });
}

// 日常记录 
export function dailyStatePage(params) {
  return request({
    url: basicPath2 + "manage/dailyState/page",
    method: "post",
    data: params,
  });
}

// 饲养管理
export function feedPage(params) {
  return request({
    url: basicPath2 + "manage/feed/page",
    method: "post",
    data: params,
  });
}

// 生长检测
export function growPage(params) {
  return request({
    url: basicPath2 + "manage/grow/page",
    method: "post",
    data: params,
  });
}

// 配种管理
export function breedingPage(params) {
  return request({
    url: basicPath2 + "manage/breeding/page",
    method: "post",
    data: params,
  });
}

// 分娩管理
export function calvingPage(params) {
  return request({
    url: basicPath2 + "manage/calving/page",
    method: "post",
    data: params,
  });
}

// 繁育记录列表
export function cowBreedStatsList(params) {
  return request({
    url: basicPath2 + "cowBreedingAnalysis/statsList",
    method: "post",
    data: params,
  });
}
// 繁育统计
export function cowBreedSingleStats(params) {
  return request({
    url: basicPath2 + "cowBreedingAnalysis/singleStats",
    method: "post",
    data: params,
  });
}

/**
 * 资金管理
 */

export function purchaseSupervisePage(params) {
  return request({
    url: basicPath10 + "purchaseSupervise/page",
    method: "post",
    data: params,
  });
}

// 新增资金数据
export function purchaseSuperviseAdd(params) {
  return request({
    url: basicPath10 + "purchaseSupervise/add",
    method: "post",
    data: params,
  });
}

// 订单列表
export function purchaseOrderPage(params) {
  return request({
    url: basicPath10 + "purchaseOrder/page",
    method: "post",
    data: params,
  });
}

// 延长到期时间
export function updateSupervisionEndDate(params) {
  return request({
    url: basicPath10 + "purchaseSupervise/updateSupervisionEndDate",
    method: "post",
    data: params,
  });
}

// 还款确认
export function updateRepaymentStatus(params) {
  return request({
    url: basicPath10 + "purchaseSupervise/updateRepaymentStatus",
    method: "post",
    data: params,
  });
}

// 治病列表
export function healingPage(params) {
  return request({
    url: basicPath2 + "manage/healing/page",
    method: "post",
    data: params,
  });
}