import request from "@/utils/request";
import { basicPath4 } from "@/api/base";
import { rabBaseInfo } from "@/api/ffs/xmbCommon";
// 接口文档地址 https://console-docs.apipost.cn/preview/d041bbfe634ddd09/edea5edc01bedd11?target_id=299907d0-b64c-46c7-986e-2554b487f2f0
// 资金明细
export function fundDetails(data) {
  data.extsysCode = "100000010";
  return request({
    url:"nyb-service/empayOperator/qryQuotaDetail",
    method: "post",
    data: data,
  });
}


// 消费记录
export function recordsHistoryTable(data) {
  data.extsysCode = "100000010";
  return request({
    url: "nyb-service/empayOperator/quotaTransList",
    method: "post",
    data: data,
  });
}
// 还款记录
export function paymentHistoryTable(data) {
  data.extsysCode = "100000010";
  return request({
    url: "nyb-service/empayOperator/queryRepayment",
    method: "post",
    data: data,
  });
}


// 申请信息
export function fundAplyDetail(data) {
  data.extsysCode = "100000010";
  return request({ 
    url: "nyb-service/empayOperator/queryApplyDetail",
    method: "post",
    data: data,
  });
}


// 资金流向里面的导出 1消费记录 2还款记录 3资金明细
export function exportQuotaDetails(query) {
  query.extsysCode = "100000010";
  return request({ 
    url: "nyb-service/export/exportQuotaDetails",
    method: "get",
    responseType: 'blob',
    params: query
  });
}
