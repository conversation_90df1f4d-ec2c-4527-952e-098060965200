<template>
    <div class="app-container">
        <el-tabs v-model="params.fsiType" type="card" @tab-click="tabChange">
            <el-tab-pane label="活畜抵押调研表" name="3"></el-tab-pane>
            <el-tab-pane label="存货质押调研表" name="2"></el-tab-pane>
            <el-tab-pane label="无货质押调研表" name="1"></el-tab-pane>
            <el-tab-pane label="粮仓无货质押调研表" name="5"></el-tab-pane>
            <el-tab-pane label="粮仓存货质押调研表" name="4"></el-tab-pane>
        </el-tabs>
        <el-row type="flex" :gutter="10" class="mb8" justify="space-between">
            <el-col :span="1.5">
                当前调研表有效计分项合计：{{ allNum }}
                <!-- <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button> -->
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain size="mini" @click="handleExportQuot">导出</el-button>

                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>

                <el-button type="primary" plain size="mini" @click="handleGrade">分数等级设置</el-button>
            </el-col>
        </el-row>
        <el-table v-loading="loading" border :data="list" style="width: 100%" show-summary :summary-method="getSummaries">
            <el-table-column align="center" label="序号" prop="fsiNumber" />
            <el-table-column align="center" label="字段名称" prop="fsiFieldName" />
            <el-table-column align="center" label="字段描述" prop="fsiFieldDes" />
            <el-table-column align="center" label="字段类型" prop="fsiFieldType">
                <template slot-scope="scope">
                    {{ scope.row.fsiFieldType == 1 ? '选择类型' : '填写类型' }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="是否记分" prop="fsiIsScore">
                <template slot-scope="scope">
                    <el-switch @change="switchChange(scope.row)" v-model="scope.row.fsiIsScore" :active-value="1"
                        :inactive-value="2" active-color="#13ce66" inactive-color="#ff4949">
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column align="center" label="最大分值" prop="logContent">
                <template slot-scope="scope">
                    {{ handleMax(scope.row.details) }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作人" prop="createBy" />
            <el-table-column align="center" label="操作日期" prop="createTime">
                <template slot-scope="scope">
                    {{ scope.row.createTime | formatDate }}
                </template>
            </el-table-column>
            <el-table-column align="center" label="操作" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="handleExplain(scope.row)">详情
                    </el-button>
                    <el-button size="mini" type="text" icon="el-icon-edit" @click="handleEdit(scope.row)">编辑
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="30%" @close="handleClose">
            <el-form :rules="rules" ref="form" :model="form" label-width="100px" label-position="left">
                <el-form-item label="字段类型：" prop="fsiFieldType">
                    <el-radio-group v-model="form.fsiFieldType" :disabled="isDisabled || Boolean(fsiId)">
                        <el-radio :label="1">选择类型</el-radio>
                        <el-radio :label="2">填写类型</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="字段名称：" prop="fsiFieldName">
                    <el-input :disabled="isDisabled || Boolean(fsiId)" v-model="form.fsiFieldName"
                        placeholder="请输入字段名称"></el-input>
                </el-form-item>
                <el-form-item label="字段描述：" prop="fsiFieldDes">
                    <el-input :disabled="isDisabled || Boolean(fsiId)" v-model="form.fsiFieldDes"
                        placeholder="请输入字段描述"></el-input>
                </el-form-item>
                <el-form-item label="是否记分：" prop="fsiIsScore">
                    <el-radio-group v-model="form.fsiIsScore"
                        :disabled="isDisabled || Boolean(fsiId)" >
                                                                                                                                                                                                                                                                                                                        <el-radio :label="1">是</el-radio>
                        <el-radio :label="2">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="分数：" prop="fraction" v-if="form.fsiFieldType == 2">
                    <el-input :disabled="isDisabled" v-model="form.details[0].fsidScore" placeholder="请输入分数"></el-input>
                </el-form-item>
                <div class="dynamic-input" v-for="(item, index) in form.details" :key="index" v-if="form.fsiFieldType == 1">
                    <el-row :gutter="18" style="display: flex; align-items: center;">
                        <el-col :span="9">
                            <el-form-item :prop="`details.${index}.fsidOptName`">
                                <el-input :disabled="isDisabled" v-model="item.fsidOptName"
                                    :placeholder="'选项' + (index + 1)"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="9">
                            <el-form-item :prop="`details.${index}.fsidScore`">
                                <el-input :disabled="isDisabled" v-model="item.fsidScore" placeholder="分数"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-button :disabled="isDisabled" class="icon-button" icon="el-icon-plus" circle
                                @click="handleAddInput()"></el-button>
                            <el-button :disabled="isDisabled" class="icon-button" icon="el-icon-minus" circle
                                @click="handleDelInput(index)"></el-button>
                        </el-col>
                    </el-row>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submit">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog title="分数等级设置" :visible.sync="fractionVisible" width="45%" @close="handleGradeClose">
            <div> 当前调研表有效计分项合计：{{ allNum }}</div>
            <el-form :model="fractionForm" label-position="top" :inline="true">
                <template v-for="(item, index) in fractionForm.grade" >
                    <el-row :gutter="8" >
                        <el-col :span="6">
                            <el-form-item label="分数区间" :key="index" :prop="`grade.${index}.fsisStart`">
                                <el-input type="number" v-model="item.fsisStart" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="1" style="text-align: center;padding-top:53px;padding-right:10px;">
                            -
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="分数区间" :key="index" :prop="`grade.${index}.fsisEnd`">
                                <el-input type="number" v-model="item.fsisEnd" placeholder="请输入"></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- fsisGrade -->
                        <el-form-item label="等级" :key="index" :prop="`grade.${index}.fsisGrade`">
                            <el-radio-group v-model="item.fsisGrade">
                                <el-radio v-for="(items, indexs) in gradeData" :key="indexs" :label="items.label"
                                    :value="items.label"></el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-row>

                </template>
                <div style="text-align: right;">
                    <el-button type="text" @click="handleGradeDynamic">添加</el-button>
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="fractionVisible = false">取 消</el-button>
                <el-button type="primary" @click="frationSubmit">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>

<script>
import { exportExcel, viewFile } from "@/utils/east";
import { exportRecord } from "@/api/ffs/farmingSupervisionSheet/livingSupervisionApi";
import { addSit, sitPage, updateSit, sitAddScores, editScoresSubmit, sitEdit, editScores } from "@/api/ffs/survey/index.js"
export default {
    filters: {
        formatDate(value) {
            if (value == undefined) {
                return;
            }
            // let date = new Date(value * 1000);
            let date = new Date(value);
            //时间戳为10位需*1000，时间戳为13位的话不需乘1000
            let y = date.getFullYear();
            let MM = date.getMonth() + 1;
            MM = MM < 10 ? ('0' + MM) : MM; //月补0
            let d = date.getDate();
            d = d < 10 ? ('0' + d) : d; //天补0
            let h = date.getHours();
            h = h < 10 ? ('0' + h) : h; //小时补0
            let m = date.getMinutes();
            m = m < 10 ? ('0' + m) : m; //分钟补0
            let s = date.getSeconds();
            s = s < 10 ? ('0' + s) : s; //秒补0
            // return y + '-' + MM + '-' + d; //年月日
            return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s; //年月日时分秒
        }
    },
    components: {},
    computed: {
        allNum() {
            let a = 0
            this.list.forEach(item => {
                item.details.forEach(items => {
                    a += items.fsidScore
                })
            })
            return a
        }
    },
    props: {},
    data() {
        return {

            isDisabled: false,
            fsiId: '',
            activeName: '1',
            gradeData: [
                {
                    label: 'A'
                },
                {
                    label: 'B'
                }, {
                    label: 'C'
                }, {
                    label: 'D'
                }, {
                    label: 'E'
                }
            ],
            fractionVisible: false,
            fractionForm: {
                grade: [
                ]
            },
            fraction: 100,
            dialogVisible: false,
            dialogTitle: '',
            // 遮罩层
            loading: false,
            // 总条数
            total: 0,
            // 表格数据
            list: [],
            params: {
                fsiType: "1",
            },
            rules: {

            },
            form: {
                fsiFieldType: 1,
                fsiIsScore: 1,
                fsiFieldDes: "",
                details: []
            },
            isEdit: false
        }
    },

    methods: {
        frationSubmit() {
          console.log(this.fractionForm,274)
            if (this.fractionForm.grade.length) {
                for (let i = 0; i < this.fractionForm.grade.length; i++) {
                    if (!this.fractionForm.grade[0].fsisStart) {
                        this.fractionForm.grade[0].fsisStart = 0
                    }
                    if (Number(this.fractionForm.grade[i].fsisStart) > Number(this.fractionForm.grade[i].fsisEnd)) {
                        this.$message.error('请输入正确的分数段');
                        return;
                    }
                    // console.log(Boolean(Number(this.fractionForm.grade[i].fsisStart) != 0), Boolean(this.fractionForm.grade[i].fsisStart != 0 && this.fractionForm.grade[i].fsisEnd == ''), this.fractionForm.grade[i].fsisEnd, this.fractionForm.grade[i].fsisGrade);
                    if (!this.fractionForm.grade[i].fsisEnd || !this.fractionForm.grade[i].fsisGrade) {
                        this.$message.error('请完善分数等级');
                        return;
                    }
                    this.fractionForm.grade[i].fsisType = this.params.fsiType
                }
            }
            if (this.fractionForm.grade.length > 1) {
                for (let i = 1; i < this.fractionForm.grade.length; i++) {
                    // console.log(this.fractionForm.grade[i].fsisStart, this.fractionForm.grade[i - 1].fsisEnd);
                    if (this.fractionForm.grade[i].fsisStart != this.fractionForm.grade[i - 1].fsisEnd) {
                        this.$message.error('分数区间存在缺口，需完善后方可保存')
                        // break;
                        return
                    }
                }
            }

            if (!this.isEdit) {

                sitAddScores(this.fractionForm.grade).then(res => {
                    if (res.code === 200) {
                        this.$message.success('设置成功')
                        this.getList()
                    }
                })
            } else {
                const params = {
                    fsisType: this.params.fsiType,
                    scores: this.fractionForm.grade
                }
                editScoresSubmit(params).then(res => {
                    if (res.code === 200) {
                        this.$message.success('设置成功')
                        this.getList()
                    }
                })
            }

            this.fractionVisible = false
        },
        handleGradeClose() {
            this.fractionForm.grade = []
        },
        handleGrade() {
            this.fractionVisible = true
            editScores({ id: this.params.fsiType }).then(res => {
                if (res.result.length) {
                    this.isEdit = true
                    res.result.forEach(item => {
                        this.fractionForm.grade.push({
                            fsisStart: item.fsisStart,
                            fsisEnd: item.fsisEnd,
                            fsisGrade: item.fsisGrade
                        })
                    })
                } else {
                    this.fractionForm.grade.push({
                        fsisStart: '',
                        fsisEnd: '',
                        fsisGrade: ''
                    })
                }
            })
        },
        //导出
        handleExportQuot() {
        },
        tabChange(value) {
            this.getList()
        },
        handleMax(arr) {
            let maxArr = []
            arr.forEach(item => {
                maxArr.push(item.fsidScore)
            })
            if (maxArr.length == 1) {
                return maxArr[0]
            } else {
                return Math.max.apply(null, maxArr)
            }
        },
        handleClose() {
            this.reset()
        },
        reset() {
            this.resetForm("form")
            this.form = {
                fsiType: this.params.fsiType,
                fsiFieldType: 1,
                fsiIsScore: 1,
                fsiFieldName: '',
                fsiFieldDes: "",
                details: [
                    // {
                    //     fsidOptName: '',
                    //     fsidScore: ''
                    // }
                ]
            }

        },
        switchChange(row) {
            row.fsiIsScore == 1 ? 1 : 2
            console.log(row);
            const { details } = row
            delete row.details
            const params = {
                sit: { ...row },
                details
            }
            updateSit(params).then(res => {
                if (res.code === 200) {
                    this.$message({
                        message: '修改成功',
                        type: 'success'
                    });
                    this.getList()
                }
            })
        },
        async getList() {
            this.loading = true
            const res = await sitPage(this.params)
            this.list = res.result
            this.loading = false
        },
        handleGradeDynamic() {
            this.fractionForm.grade.push({
                fsisStart: '',
                fsisEnd: '',
                fsisGrade: '',
            })
        },
        submit() {
            if (this.fsiId && this.isDisabled) {
                this.dialogVisible = false
                return
            }
            let params = {}
            if (this.form.fsiFieldType == 2) {
                this.form.details[0].fsidOptName = this.form.fsiFieldDes
            }
            let form = JSON.parse(JSON.stringify(this.form))
            const { details, ...rest } = form
            params.sit = { ...rest }
            params.details = details
            console.log(this.form, form, params);
            if (!this.fsiId) {
                params.sit.fsiNumber = this.list.length + 1
                params.sit.fsiType = this.params.fsiType;

                    addSit(params).then(res => {
                        if (res.code === 200) {
                            this.$message({
                                message: '添加成功',
                                type: 'success'
                            });
                            this.getList()
                        }
                    })
            } else {
                sitEdit(params).then(res => {
                    if (res.code === 200) {
                        this.$message({
                            message: '编辑成功',
                            type: 'success'
                        });
                        this.getList()
                    }
                })
            }

            this.dialogVisible = false
        },
        handleExplain(rows) {
            this.isDisabled = true
            this.fsiId = rows.fsiId
            let row = { ...rows }
            row.fsiType = String(row.fsiType)
            this.dialogVisible = true
            this.dialogTitle = '编辑'
            const data = []
            row.details.forEach(item => {
                data.push({
                    fsidOptName: item.fsidOptName,
                    fsidScore: item.fsidScore
                })
            })
            delete row.details
            this.form = {
                ...row,
                details: data
            }

        },
        handleEdit(rows) {
            this.isDisabled = false
            this.fsiId = rows.fsiId
            let row = { ...rows }
            row.fsiType = String(row.fsiType)
            this.dialogVisible = true
            this.dialogTitle = '编辑'
            const data = []
            row.details.forEach(item => {
                data.push({
                    fsidOptName: item.fsidOptName,
                    fsidScore: item.fsidScore
                })
            })
            delete row.details
            this.form = {
                ...row,
                details: data
            }
            console.log(rows, this.form, data);
        },
        handleDelDynamic(index) {
            if (this.form.every.length === 1) {
                this.$message({
                    message: '最后一条不可删除',
                    type: 'error'
                });
                return
            }
            this.form.every.splice(index, 1)
        },

        handleAdd() {
            this.fsiId = ''
            this.isDisabled = false
            console.log(this.isDisabled, Boolean(this.fsiId));
            this.reset()
            this.dialogVisible = true
            this.dialogTitle = '新增'
            this.form.details.push({
                fsidOptName: '',
                fsidScore: ''
            })
        },
        handleDelInput(index) {
            if (this.form.details.length <= 1) {
                this.$message({
                    message: '最后一条不可删除',
                    type: 'error'
                });
                return
            }
            this.$delete(this.form.details, index)
        },
        handleAddInput() {
            this.form.details.push({
                fsidOptName: '',
                fsidScore: ''
            })
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                let a = 0
                data.forEach(item => {
                    a += this.handleMax(item.details)
                })
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                if (column.property == 'logContent') {
                    sums[index] = a + '分';
                } else {
                    sums[index] = 'N/A';
                }
            });
            return sums;
        }
    },
    created() {
        this.getList()
    },
    mounted() {

    },
}
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
    margin-bottom: 10px !important;
}

:deep(.icon-button) {
    padding: 4px !important;
}

.dynamic {
    padding: 20px;
    background-color: #f2f2f2;
    position: relative;
    margin-top: 20px;

    .all-minus {
        position: absolute;
        right: -10px;
        top: -10px;
    }
}


.dynamic-button {
    background-color: white !important;
    border: none;
    border-radius: 100px 100px 100px 100px;
    color: black;
}

.dynamic-input {
    :deep(.el-form-item__content) {
        margin-left: 0 !important;
    }
}
</style>
