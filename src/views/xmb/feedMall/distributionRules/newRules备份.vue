<template>
  <div class="app-container">
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="规则名称" prop="freightName" class="rule-input">
        <el-input
          v-model="ruleForm.freightName"
          placeholder="请输入规则名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="freightStatus">
        <el-radio-group v-model="ruleForm.freightStatus">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="2">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <div class="set-rule">
      <el-form
        :model="ruleData"
        ref="ruleDataForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <div class="title"><span style="color: red"> * </span> 包邮条件</div>
        <el-card
          class="box-card"
          shadow="never"
          v-for="(item, index) in ruleData.cardForm"
          :key="index"
        >
          <el-form-item
            label="活动条件"
            class="condition"
            :prop="'cardForm.' + index + '.freightType'"
            :rules="{
              required: true,
              message: '请选择活动条件',
              trigger: ['blur', 'change'],
            }"
          >
            <el-radio-group v-model="item.freightType">
              <el-radio :label="1">满元</el-radio>
              <el-radio :label="2">满重</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :prop="'cardForm.' + index + '.number'">
            <el-input
              type="number"
              placeholder="请输入内容"
              v-model="item.price"
              class="tip-rule"
              @blur="changeInput(item.price)"
            >
              <template v-if="item.freightType == 1" slot="append">元</template>
              <template v-if="item.freightType == 2" slot="append">KG</template>
            </el-input>
            <div
              class="tips"
              v-if="item.freightType == 1 || item.freightType == 2"
            >
              请输入≥0的数字，支持小数点后两位
            </div>
          </el-form-item>
          <el-form-item
            label="包邮地区"
            :prop="'cardForm.' + index + '.areaRoiesLenth'"

          >
            <!-- <el-cascader
              v-model="item.areaRoiesLenth[index]"
              :options="options"
              @change="handleChange(index)"
              @visible-change="
                (v) => {
                  handleVisibleChange(v, index);
                }
              "
              placeholder="请选择可配送的区域"
              clearable
              collapse-tags
              :props="{ multiple: true }"
              :ref="'cascaderAddr' + index"
              class="cascader"
            >
            </el-cascader> -->
            <div style="display: flex;" v-if='options?.length' class="area">
              <ul>
                <li v-for='(item,index1) in options' :key='index1' :class="i == index1 ? 'current-tab' : 'default-tab'" >
                  <el-checkbox :disabled="item.dataIndex!=index&&item.dataIndex!=null" v-model="item.check" @change="handle0(item,index1,index)" :indeterminate="item.indeterminate">
                     <span :class="i == index1 ? 'current-lable' : 'default-tab'" @click.prevent="i=index1">{{item.label}}</span>
                  </el-checkbox>
                </li>
              </ul>
              <ul>
                <li v-for='(item,index2) in options[i].children' :key='index2' :class="i1 == index2 ? 'current-tab' : 'default-tab'">
                  <el-checkbox :disabled="item.dataIndex!=index&&item.dataIndex!=null"  v-model="item.check" @change="handle1(item,index2,options[i],index)" :indeterminate="item.indeterminate">
                    <span :class="i1 == index2 ? 'current-lable' : 'default-tab'"  @click.prevent="i1=index2">{{item.label}}</span>
                  </el-checkbox>

                </li>
              </ul>
              <ul>
                <li v-for='(item,index3) in options[i].children[i1].children' :key='index3'>
                  <el-checkbox :disabled="item.dataIndex!=index&&item.dataIndex!=null"  v-model="item.check" @change="handle2(item,index3,options[i].children[i1],options[i],index)">
                    <span >{{item.label}}</span>
                  </el-checkbox>
                </li>
              </ul>
            </div>
          </el-form-item>
          <div class="box-card-rule">
            <!-- this.ruleData.cardForm[index].areaEcho -->
            <div
              v-if="ruleData.cardForm[index].areaEcho.length > 0"
              class="checked-list"
            >
              <div
                v-for="(val, idx) in JSON.parse(
                  ruleData.cardForm[index].areaEcho
                )"
                :key="idx"
                class="checked-item"
              >
                <span v-if="val.checked">
                  <el-button slot="reference">{{ val.label }}</el-button>
                </span>
                <span v-else>
                  <el-popover
                    placement="right"
                    width="400"
                    popper-class="area_popper"
                    trigger="hover"
                  >
                    <el-button slot="reference"
                      >{{ val.label }}（部分地区）</el-button
                    >
                    <div v-for="(v, i) in val.children" :key="i">
                      <div>
                        <div class="city">{{ v.label }}</div>
                        <div
                          class="third-item"
                          v-for="(val, idx) in v.children"
                          :key="idx"
                        >
                          * {{ val.label }}
                        </div>
                      </div>
                    </div>
                  </el-popover>
                </span>
              </div>
            </div>
            <div v-else>全部可配送地区</div>
          </div>
          <el-button
            v-if="ruleData.cardForm.length > 1"
            class="delete-rule"
            @click="handleDeleteRules(index)"
            >删除</el-button
          >
        </el-card>
        <el-form-item class="oper-rules">
          <el-button
            v-if="ruleData.cardForm.length < 5"
            type="primary"
            @click="addRules(ruleData.cardForm.length)"
            >新增包邮条件</el-button
          >
        </el-form-item>
        <el-form-item class="oper-rules">
          <el-button @click="resetForm">取消</el-button>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { areaData } from "@/utils/mixin/area.js";
import {
  insertGoodsFreight,
  selectGoodsFreightInfo,
  updateGoodsFreight,
  selectDeliveryArea,
} from "@/api/xmb/feedMall/goodsManage/distribution";
import provincialLevel from "@/utils/provincialLevel.js";

export default {
  name: "newRules",
  mixins: [areaData],
  components: {},

  data() {
    return {
      ruleForm: {
        freightName: "",
        freightStatus: "",
      },
      provincialLevel: provincialLevel,
      ruleData: {
        cardForm: [
          {
            freightType: 1,
            price: "",
            areaRoiesLenth: [],
            areaList: [],
            areaEcho: [],
          },
        ],
      },
      templeteId: "",
      flagType: "",
      rules: {
        freightName: [
          { required: true, message: "请输入规则名称", trigger: "blur" },
        ],
        freightStatus: [
          { required: true, message: "请选择启用状态", trigger: "change" },
        ],
      },
      ruleDataForm: {
        freightType: [
          { required: true, message: "请选择活动条件", trigger: "change" },
        ],
        areaRoiesLenth: [
          { required: true, message: "请选择包邮地区", trigger: "change" },
        ],
      },
      options: [],
      flagList: [],
      disabled: false,
      isEdit: false,

      i:0,
      i1:0
    };
  },
  filters: {},
  created() {
    // console.log(this.$route.query);
    this.templeteId = this.$route.query.templeteId;
    this.flagType = this.$route.query.type;
    if (this.flagType == 2) {
      this.getTempleteDetail(this.templeteId);
      this.isEdit = true
    }
    this.getDefaultArea();
  },
  watch: {
    options:{
      handler(newValue, oldValue) {
        console.log(111111)
          console.log(newValue)
      },
      deep:true
    }
  },
  methods: {
    handle0(item,index,dataIndex){
      console.log(item)
      // item.indeterminate=false
      // item.children.indeterminate=!item.check
      // if(item.check){
      //   item.children.indeterminate=false
      // }
      // this.i=index
      // item.check=true
      for (let i=0;i<item.children.length;i++) {
          item.children[i].check=item.check
          if(item.check){
            item.dataIndex=dataIndex
            item.children[i].dataIndex=dataIndex
          }else{
            item.dataIndex=null
            item.children[i].dataIndex=null
          }
          for (let j=0;j<item.children[i].children.length;j++) {
              if(item.check){
                item.children[i].children[j].dataIndex=dataIndex
              }else{
                item.children[i].children[j].dataIndex=null
              }

              item.children[i].children[j].check=item.check

          }
      }
      this.$forceUpdate()

    },
    handle1(item,index,parnet,dataIndex){
      // this.i1=index
      parnet.indeterminate=item.check
      // item.indeterminate=false
      //

      // item.check=true
      let checkCount=0
      for (let i=0;i<parnet.children.length;i++) {
        if(parnet.children[i].check){
          checkCount++
          parnet.indeterminate=true
        }
        if(!parnet.children[i].check){
          parnet.check=false
        }

      }
      if(checkCount==parnet.children.length){
        console.log(111)
        parnet.check=true
        parnet.indeterminate=false
      }
      if(checkCount==0){
        parnet.check=false
        parnet.indeterminate=false
      }
      for (let i=0;i<item.children.length;i++) {
          item.children[i].check=item.check
      }
      this.$forceUpdate()
    },
    handle2(item,index,parnet,faparent,dataIndex){
      let checkCount=0
      if(item.check){
        item.dataIndex=dataIndex
      }else{
        item.dataIndex=null
      }
      // 市
      for (let i=0;i<parnet.children.length;i++) {
        if(parnet.children[i].check){
          checkCount++
          parnet.indeterminate=true
          faparent.indeterminate=true
        }
        if(!parnet.children[i].check){
          parnet.check=false
        }

      }

      if(checkCount==parnet.children.length){
        console.log(111)
        parnet.check=true
        parnet.indeterminate=false
      }
      if(checkCount==0){
        parnet.check=false
        parnet.indeterminate=false
      }

      // 省
      let checkCount1=0
      let indeterminateCoutn=0
      for (let j=0;j<faparent.children.length;j++) {
        if(faparent.children[j].check||faparent.children[j].indeterminate){
          // indeterminateCoutn++
          faparent.indeterminate=true
        }
        if(faparent.children[j].check){
          checkCount1++
        }
        if(faparent.children[j].indeterminate){
          indeterminateCoutn++
        }
        if(!faparent.children[j].check){
          faparent.check=false
        }

      }
      if(checkCount1==faparent.children.length){
        console.log(111)
        faparent.check=true
        faparent.indeterminate=false
      }
      if(indeterminateCoutn>0||parnet.check){
        faparent.indeterminate=true
      }else{
        faparent.indeterminate=false
      }
      if(checkCount1==0){
        faparent.check=false
        // faparent.indeterminate=false
      }
      // parnet.indeterminate=item.check
      // faparent.indeterminate=item.check
      // console.log(parnet)

      // for (let i=0;i<parnet.children.length;i++) {
      //     parnet.children[i].check=parnet.check
      // }
      // item.check=true

      this.$forceUpdate()
    },
    /**
     *
     * @param {互斥逻辑} val
     */
    handleVisibleChange(visible, index) {
      // console.log(visible)
      if (this.isEdit) {
        return;
      }
      this.calcAreaPropsFinal(index);
    },
    calcAreaPropsFinal(index) {
      if (!this.ruleData || !this.ruleData.cardForm) {
        return;
      }
      const selectedArea = [];
      // for (let i = 0; i < this.ruleData.cardForm.length; i++) {
      //   if (i === index) {
      //     continue;
      //   }
      //   const form = this.ruleData.cardForm[i];
      //   if (!form.areaRoiesLenth[i]) {
      //     continue;
      //   }
      //   for (let j = 0; j < form.areaRoiesLenth[i].length; j++) {
      //     const areaItem = form.areaRoiesLenth[i][j];
      //     const checkedArea = areaItem[areaItem.length - 1];
      //     if(!selectedArea.includes(areaItem[0])){
      //       selectedArea.push(areaItem[0]);
      //     }
      //     if(!selectedArea.includes(areaItem[1])){
      //       selectedArea.push(areaItem[1]);
      //     }
      //     selectedArea.push(checkedArea);
      //   }
      // }
      // for (const l1 of this.options || []) {
      //   if (selectedArea.includes(l1.value)) {
      //     l1.disabled = true;
      //   } else {
      //     l1.disabled = false;
      //   }
      //   if (!l1.children) {
      //     continue;
      //   }
      //   for (const l2 of l1.children || []) {
      //     l2.disabled = false;
      //     if (selectedArea.includes(l2.value)) {
      //       l2.disabled = true;
      //     }
      //     if (!l1.children) {
      //       continue;
      //     }
      //     for (const l3 of l2.children || []) {
      //       l3.disabled = false;
      //       if (selectedArea.includes(l3.value)) {
      //         l3.disabled = true;
      //       }
      //     }
      //   }
      // }



      let disabledObj = {}
      console.log(this.ruleData.cardForm)
      this.ruleData.cardForm.forEach(i => {
        console.log(i.areaList)
        i.areaList.forEach(item => {
        this.options.forEach(item1 => {
          // item1.disabled = false
            item1.children.forEach(item2 => {
                // item2.disabled = false
                item2.children.forEach(item3 => {
                        // item3.disabled = false
                        if (item.districtId == item3.value) {
                            if (disabledObj[item.cityName]) {
                              if (!disabledObj[item.cityName].includes(item.districtName)) {
                                disabledObj[item.cityName].push(item.districtName)
                              }
                            } else {
                              disabledObj[item.cityName] = [item.districtName]
                            }
                            item3.disabled = true
                        }
                        if (disabledObj[item.cityName] && disabledObj[item.cityName].length === item2.children.length && item.cityId == item2.value) {
                            item2.disabled = true;
                            if (disabledObj[item.provinceName]) {
                              if (!disabledObj[item.provinceName].includes(item.cityName)) {
                                disabledObj[item.provinceName].push(item.cityName)
                              }
                            } else {
                              disabledObj[item.provinceName] = [item.cityName]
                            }
                        }
                        if (disabledObj[item.provinceName] && disabledObj[item.provinceName].length === item1.children.length && item.provinceId == item1.value) {
                            item1.disabled = true;
                        }
                    })
                })

            })
        })
      })
    },
    // 新增单个包邮条件
    addRules(val) {
      // console.log(val);
      // if (this.ruleData.cardForm[this.ruleData.cardForm.length - 1].areaList.length == 0
      //     || !this.ruleData.cardForm[this.ruleData.cardForm.length - 1].price
      // )
      // if (!this.ruleData.cardForm[this.ruleData.cardForm.length - 1].price
      // ) {
      //   this.$message.info("您还有未设置的包邮条件");
      //   return
      // }
      if (this.ruleData.cardForm.length > 4) {
        this.$message.info("最多可增加5个包邮条件");
      } else {
        this.isEdit = false
        this.ruleData.cardForm.push({
          freightType: 1,
          areaRoiesLenth: [],
          price: "",
          areaList: [],
          areaEcho: [],
        });
      }
      // console.log(this.$store.state.orderRules);
      // console.log(this.$store.state.orderRules.rules);
      // let checkedList = this.$store.state.orderRules.rules;
    },

    handleChange(index) {
      let cascaderAddr = this.$refs["cascaderAddr" + index];
      cascaderAddr[0].getCheckedNodes();
      let address = cascaderAddr[0].getCheckedNodes();
      let checkedNodeList = address.filter(
        (item) => !(item.parent && item.parent.checked)
      );
      // console.log(checkedNodeList)
      let areaList = [];
      address.map((item) => {
        if (item.level == 3) {
          areaList.push({
            provinceId: item.parent.parent.value,
            provinceName: item.parent.parent.label,
            cityId: item.parent.value,
            cityName: item.parent.label,
            districtId: item.value,
            districtName: item.label,
          });
        }
      });
      // console.log(areaList)
      this.ruleData.cardForm[index].areaList = areaList;
      // console.log(this.ruleData.cardForm[index].areaList); areaEcho

      // 重新拼接选中数据
      const nodeHash = {};
      checkedNodeList.forEach((item) => {
        if (item.level == 1) {
          if (item?.children) {
            let arrObj = [];
            let areaObj = [];
            item.children.forEach((val) => {
              const area3 = [];
              if (val.children) {
                val.children.forEach((val3) => {
                  area3.push({
                    label: val3.label,
                    value: val3.value,
                    checked: val3.checked,
                  });
                });
              }
              arrObj.push({
                label: val.label,
                value: val.value,
                children: area3,
                checked: val.checked,
              });
            });
            nodeHash[item.label] = {
              label: item.label,
              value: item.value,
              checked: item.checked,
              children: arrObj,
            };
          }
        } else if (item.level == 2 && !nodeHash[item.parent.label]) {
          const area3 = [];
          item.children.forEach((val) => {
            area3.push({
              label: val.label,
              value: val.value,
            });
          });
          nodeHash[item.parent.label] = {
            label: item.parent.label,
            value: item.parent.value,
            children: [
              {
                label: item.label,
                value: item.value,
                checked: item.checked,
                children: area3,
              },
            ],
            childrenHash: {},
          };
          nodeHash[item.parent.label].childrenHash[item.label] = item.label;
        } else if (item.level == 2 && nodeHash[item.parent.label]) {
          if (!nodeHash[item.parent.label].childrenHash[item.label]) {
            const area3 = [];
            item.children.forEach((val) => {
              area3.push({
                label: val.label,
                value: val.value,
              });
            });
            nodeHash[item.parent.label].children.push({
              label: item.label,
              value: item.value,
              children: area3,
            });
            nodeHash[item.parent.label].childrenHash[item.label] = item.label;
          }
        } else if (item.level == 3 && !nodeHash[item.parent.parent.label]) {
          nodeHash[item.parent.parent.label] = {
            label: item.parent.parent.label,
            value: item.parent.parent.value,
            children: [
              {
                label: item.parent.label,
                value: item.parent.value,
                children: [
                  {
                    label: item.label,
                    value: item.value,
                  },
                ],
              },
            ],
            childrenHash: {},
          };
          nodeHash[item.parent.parent.label].childrenHash[item.parent.label] =
            item.parent.label;
        } else if (item.level == 3 && nodeHash[item.parent.parent.label]) {
          const label = nodeHash[item.parent.parent.label];
          if (item.level == 3 && !label.childrenHash[item.parent.label]) {
            label.children.push({
              label: item.parent.label,
              value: item.parent.value,
              children: [
                {
                  label: item.label,
                  value: item.value,
                },
              ],
            });
            label.childrenHash[item.parent.label] = item.parent.label;
          } else {
            label.children.forEach((i) => {
              if (i.value == item.parent.value) {
                i.children.push({
                  label: item.label,
                  value: item.value,
                });
              }
            });
          }
        }
      });
      this.ruleData.cardForm[index].areaEcho = JSON.stringify(
        Object.values(nodeHash)
      );
      console.log(Object.values(nodeHash));
      // this.$store.commit("SET_RULES", Object.values(nodeHash));
      // let treeNode = this.getLastTreeNode(Object.values(nodeHash), []);
      // console.log(treeNode);
      // this.recursionTree(treeNode, this.options);
      // console.log(this.options);
    },
    /*    getLastTreeNode(tree, node) {
      if (Array.isArray(tree)) {
        for (let index = 0; index < tree.length; index++) {
          const element = tree[index];
          if (element.checked) {
            node.push(element);
          } else {
            if (element?.children && element?.children.length != 0) {
              this.getLastTreeNode(element.children, node);
            }else {
              node.push(element);
            }
          }
        }
      }
      return node;
    },
    recursionTree(list1, list2) {
      if (Array.isArray(list2)) {
        list2?.map((item) => {
          item.disabled = false;
          list1?.map((temp) => {
            if (item.value == temp.value) {
              item.disabled = true;
            }
          });
          if (item?.children && item?.children.length != 0) {
            this.recursionTree(list1, item.children);
          } else {
            return;
          }
        });
      } else {
        return;
      }
    }, */

    // 删除规则item
    handleDeleteRules(index) {
      // console.log('删除规则'+ index)
      if (this.ruleData.cardForm.length <= 1) {
        this.$message.error("最少需要一个规则");
      } else {
        this.ruleData.cardForm.splice(index, 1);
      }
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.ruleData.cardForm) {
            if (this.ruleData.cardForm[this.ruleData?.cardForm?.length - 1]?.areaList?.length == 0
          || !this.ruleData.cardForm[this.ruleData?.cardForm?.length - 1].price
          ) {
            this.$message.error("您还有未设置完成的包邮条件");
            return
          }
            for (let key in this.ruleData.cardForm) {
              this.ruleData.cardForm[key].areaRoiesLenth = JSON.stringify(
                this.ruleData.cardForm[key]?.areaRoiesLenth.flat()
              );
            }
          }
          const params = {
            ...this.ruleForm,
            list: [...this.ruleData.cardForm],
          };
          console.log(params);
          if (this.flagType == 1) {
            insertGoodsFreight(params).then((res) => {
              // console.log(res)
              if (res.code === 200) {
                this.$message.success("保存成功");
                this.$router.go(-1);
              }
            });
          } else if (this.flagType == 2) {
            updateGoodsFreight({ templeteId: this.templeteId, ...params }).then(
              (res) => {
                if (res.code === 200) {
                  this.$message.success("保存成功");
                  this.$router.go(-1);
                }
              }
            );
          }
        } else {
          // console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm() {
      this.$refs["ruleDataForm"].resetFields();
      this.$refs["ruleForm"].resetFields();
      this.$router.go(-1);
    },
    getTempleteDetail(templeteId) {
      selectGoodsFreightInfo({ templeteId }).then((res) => {
        this.ruleForm.freightName = res.result.freightName;
        this.ruleForm.freightStatus = res.result.freightStatus;
        this.ruleData.cardForm = res.result.freightList;
        for (let index in this.ruleData.cardForm) {
          const area = [];
          /* if (index == 0) {
              area.push(JSON.parse(this.ruleData.cardForm[index].areaRoiesLenth))
            } */
          for (let i = 0; i <= index; i++) {
            if (index == i) {
              area.push(
                JSON.parse(this.ruleData.cardForm[index].areaRoiesLenth)
              );
            } else {
              area.push([]);
            }
          }
          this.ruleData.cardForm[index].areaRoiesLenth = area;
        }
      });
    },
    // 获取默认可选择的范围
    getDefaultArea() {
      selectDeliveryArea({}).then((res) => {
        if (res.result) {
          this.options = JSON.parse(res.result.areaEcho);
          for (const l1 of this.options || []) {
            l1.disabled = false;
            l1.check=false
            l1.indeterminate=false
            for (const l2 of l1.children || []) {
              l2.disabled = false;
              l2.check=false
              l2.indeterminate=false
              for (const l3 of l2.children || []) {
                l3.disabled = false;
                l3.check=false
              }
            }
          }
          console.log(this.options, '12344555')
        }
      });
    },
    changeInput(val) {
      console.log(val);
      if (val) {
        const reg =
         /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/;
        if (!reg.test(val)) {
          this.$message.info("请输入≥0的数字，支持小数点后两位");
        }
      } else {
         this.$message.info("请输入≥0的数字，支持小数点后两位");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
::v-deep input[type="number"] {
  -moz-appearance: textfield;
}
.app-container {
  width: 800px;
  .rule-input {
    :deep(.el-input__inner) {
      width: 320px;
    }
  }
}
.set-rule {
  display: flex;
  flex-direction: column;
  .title {
    width: 100px;
    margin-left: 25px;
  }
}
.box-card {
  width: 100%;
  display: flex;
  padding-left: 25px;
  margin: 10px 0 15px 60px;
  position: relative;
  .condition {
    :deep(.el-form-item__content) {
      display: flex;
      flex-direction: column;
      padding-top: 11px;
      .tip-rule {
        margin-top: 10px;
      }
    }
  }
  .el-card {
    margin-left: 15px;
    width: 600px;
    .el-card__body {
      padding: 0;
    }
  }
  .tip-rule {
    width: 330px;
  }
  .tips {
    font-size: 14px;
    color: #ccc;
  }
}
.box-card-rule {
  width: 500px;
  border: 1px solid #e6ebf5;
  min-height: 60px;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
.oper-rules {
  :deep(.el-form-item__content) {
    margin-left: 60px !important;
  }
}
.delete-rule {
  position: absolute;
  top: 20px;
  right: 20px;
}
.checked-list {
  display: flex;
  flex-wrap: wrap;
  .checked-item {
    margin: 5px;
  }
}
.city {
  font-size: 16px;
  color: #000;
}
.third-item {
  margin: 2px 0 2px 20px;
}
:deep(.el-cascader__tags :last-child){
  display: none !important;
}
.area{
      margin: 5px 0;
      font-size: 14px;
      background: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}
.area ul{
  list-style: none;
  padding: 0;
  margin: 0;
}
.area li{
  padding:10px 25px;
}
.current-tab{
  color: #1890ff;
  background: #F5F7FA;


}
.current-lable{
  color: #1890ff;
  background: #F5F7FA;


}
.current-tab:focus{
  background: #F5F7FA;

}
</style>
