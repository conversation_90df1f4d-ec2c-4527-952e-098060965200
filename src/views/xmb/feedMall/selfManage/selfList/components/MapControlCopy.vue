<!--  -->
<template>
  <div class="main">
    <!-- <el-button type="success" :disabled="disabled" size="mini" @click="getLonLat">按地址查询坐标</el-button> -->
    <div id="container"></div>
  </div>
</template>

<script>
var map = null;
var geoc = null;
export default {
  name: "MapControl",
  props: {
    address: {
      // type: Object,
      default: {
        name: "",
        nameArr: [],
        point: {
          lat: "",
          lng: "",
        },
      },
    },
    addressInfo: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {
    //实例化地图
    map = new BMapGL.Map("container");
    map.centerAndZoom(new BMapGL.Point(116.331398, 39.897445), 13);
    geoc = new BMapGL.Geocoder();
    var zoomCtrl = new BMapGL.ZoomControl(); // 添加缩放控件
    map.addControl(zoomCtrl);

    var cityCtrl = new BMapGL.CityListControl(); // 添加城市选择
    map.addControl(cityCtrl);
    map.enableScrollWheelZoom(true); //开启鼠标滚轮缩放
    // 鼠标点击地图获取地址
    map.addEventListener("click", (e) => {
      // //清除地图上所有的覆盖物
      map.clearOverlays();
      var pt = e.latlng;
      geoc.getLocation(pt, (rs) => {
        var addComp = rs.addressComponents;
        var address =
          addComp.province +
          addComp.city +
          addComp.district +
          addComp.street +
          addComp.streetNumber;
        pt.addComp = addComp;
        map.addOverlay(new BMapGL.Marker(pt, { title: address }));
        this.setLable(address, pt);
        this.$emit("mapPosition", pt);
        console.log("pt666:", pt);
        console.log("addComp：", addComp);
      });
    });
    this.$nextTick(() => {
      setTimeout(() => {
        // 阻止点击地图城市选择控件冒泡产生bug
        let popup_close = document.getElementById("popup_close");
        popup_close.onclick = function (e) {
          e.preventDefault();
        };
      }, 2000);
    });
  },
  watch: {
    address: {
      immediate: true,
      handler(val) {
        if (typeof val.point == "undefined" || val.point.lat == "") {
          return;
        }
        this.setLable(val.name, val.point);
        map.centerAndZoom(new BMapGL.Point(val.point.lng, val.point.lat), 10);
      },
    },
  },
  methods: {
    getLonLat() {
      // 将地址解析结果显示在地图上，并调整地图视野
      console.log(111);
      if (!this.address.name) {
        this.$message({
          message: "请先选择省区市、并填写详细地址",
          type: "error",
        });
        return;
      }

      // if (this.addressInfo == '') {
      //     this.$message({
      //         message: "请先填写详细地址",
      //         type: "error",
      //     });
      //     return
      // }
      map.clearOverlays();
      geoc.getPoint(
        this.address.name + this.addressInfo,
        (point) => {
          if (!point) {
            this.$message({
              message: "您选择的地址没有解析到结果！",
              type: "error",
            });
            return;
          }
          const address =
            this.address.nameArr[0] +
            this.address.nameArr[1] +
            this.address.nameArr[2] +
            this.addressInfo;
          map.centerAndZoom(point, 16);
          map.addOverlay(new BMapGL.Marker(point, { title: address }));
          this.setLable(this.addressInfo, point);
          this.$emit("mapPosition", point);
        },
        this.address.nameArr[1]
      );
    },

    // 设置Marker旁边的lable
    setLable(address, point) {
      var label = new BMapGL.Label(address, {
        // 创建文本标注
        position: point, // 设置标注的地理位置
        offset: new BMapGL.Size(10, 20), // 设置标注的偏移量
      });
      map.addOverlay(label);
    },
  },
};
</script>
<style lang="scss" scoped>
#container {
  width: 100%;
  height: 500px;
  overflow: hidden;
  margin-top: 10px;
}
</style>
