<template>
  <div class="app-container">
    <HeadForm :queryParams="queryParams">
      <el-form-item label="问诊人" prop="userName">
        <el-input v-model="queryParams.userName" placeholder="请输入问诊人姓名" clearable />
      </el-form-item>
      <el-form-item label="问诊人手机号" prop="phoneNumber">
        <el-input v-model="queryParams.phoneNumber" placeholder="请输入问诊人手机号" clearable />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select clearable v-model="queryParams.status" placeholder="请选择状态">
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item label="问诊时间" prop="createDate">
        <el-date-picker v-model="queryParams.createDate" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" clearable end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
    </HeadForm>
    <el-card shadow="never">
      <el-table v-loading="loading" :data="list" border>
        <el-table-column type="index" align="center" width="55" label="序号"></el-table-column>
        <el-table-column label="标题" align="center" prop="title" min-width="200" />
        <el-table-column label="活畜类别" min-width="200" align="center" prop="liveAnimalsType" >
          <template slot-scope="scope">
            <div>{{ ['', '牛', '羊'][scope.row.liveAnimalsType] }}</div>
          </template>
        </el-table-column>
        <el-table-column label="病症描述"  align="center" prop="symptomDesc" width="180" >
          <template slot-scope="scope">
            <el-popover
              placement="top-start"
              width="300"
              trigger="hover"
              :content="scope.row.symptomDesc">
              <div slot="reference" class="ellipsis" style="width: 100%;">{{ scope.row.symptomDesc }}</div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="问诊图片" :width="180" prop="picAddr">
          <template slot-scope="scope">
            <div v-show="scope.row.picAddrList.length" style="width: 100%;display: flex;
            align-items: center;">
              <el-image 
                v-for="(item, index) in scope.row.picAddrList"
                :key="index"
                style="width: 60px; height: 60px;margin-right: 6px;"
                :src="item" 
                v-show="scope.row.showImage || index<1"
                :preview-src-list="scope.row.picAddrList">
              </el-image>
              <i class="el-icon-d-arrow-right pointer" v-if="scope.row.picAddrList.length > 1" v-show="!scope.row.showImage" @click="$set(scope.row, 'showImage', true)"></i>
              <i class="el-icon-d-arrow-left pointer" v-if="scope.row.picAddrList.length > 1" v-show="scope.row.showImage" @click="$set(scope.row, 'showImage', false)"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="videoAddr"  width="180" label="问诊视频">
          <template slot-scope="scope">
            <div v-show="scope.row.videoAddrList.length" style="display: flex;align-items: center;">
                <video
                  v-for="(item, index) in scope.row.videoAddrList"
                  :key="index" @click="previewVideo(item)"
                  style="margin-right: 6px;height: 60px;width: 60px;border: 1px solid #eee;"
                  disablePictureInPicture
                  :src="item" 
                  v-show="scope.row.showVideo || index<1">
                  加载失败
                </video>
              <i class="el-icon-d-arrow-right pointer" v-if="scope.row.videoAddrList.length > 1" v-show="!scope.row.showVideo" @click="$set(scope.row, 'showVideo', true)"></i>
              <i class="el-icon-d-arrow-left pointer" v-if="scope.row.videoAddrList.length > 1" v-show="scope.row.showVideo" @click="$set(scope.row, 'showVideo', false)"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="问诊人" align="center" prop="userName" min-width="160" />
        <el-table-column label="问诊人手机号" align="center" prop="phoneNumber" min-width="160" />
        <el-table-column label="问诊时间" align="center" prop="createTime" min-width="160" />

        <el-table-column label="状态" align="center" min-width="150">
          <template slot-scope="scope">
            <span>{{ ['待处理', '已处理'][scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="right" class-name="small-padding fixed-width" width="120" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="replayItem(scope.row)" v-if="scope.row.status == '0'">回复</el-button>
            <el-button size="mini" type="text" @click="openInfo(scope.row)">问诊信息</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <pagination :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList">
    </pagination>

    <el-dialog title="问诊信息" :visible.sync="infoVisible">
      <el-form label-width="100px">
        <el-form-item label="标题：">
          {{ infoData.title }}
        </el-form-item>
        <el-form-item label="活畜类别：">
          {{ ['', '牛', '羊'][infoData.liveAnimalsType] }}
        </el-form-item>
        <el-form-item label="问诊时间：">
          {{ infoData.createTime }}
        </el-form-item>
        <el-form-item label="病症描述：">
          <div style="line-height: 1.5;">{{ infoData.symptomDesc }}</div>
        </el-form-item>
        <el-form-item label="问诊图片：">
          <el-image 
            v-for="(item, index) in (infoData.picAddrList||[])"
            :key="index"
            style="width: 60px; height: 60px;margin-right: 6px;"
            :src="item" 
            :preview-src-list="infoData.picAddrList">
          </el-image>
        </el-form-item>
        <el-form-item label="问诊视频：">
          <div style="display: flex;align-items: center;flex-wrap: wrap;">
            <video
              v-for="(item, index) in (infoData.videoAddrList||[])"
              :key="index" @click="previewVideo(item)"
              style="margin-right: 6px;height: 60px;width: 60px;border: 1px solid #eee;"
              disablePictureInPicture
              :src="item" >
              加载失败
            </video>
            </div>
        </el-form-item>
        <el-form-item label="诊断：" v-if="infoData.status == 1">
          <div style="line-height: 1.5;">{{ infoData.diagnosisResult }}</div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  cattleHealthList,
  cattleHealthDetail,
  cattleHealthReply,
} from '@/api/xmb/cattleHealth'
import { listUser } from '@/api/system/user'
import Form from '@/views/nlb/components/form.vue'
import HeadForm from '@/components/HeadForm/index'
import Account from '@/views/system/platform/components/account.vue'
import { tableUi } from '@/utils/mixin/tableUi.js'
import { getFilePath, picPath, getDateTime } from '@/utils/east.js'
export default {
  mixins: [tableUi],
  components: {
    Form,
    HeadForm,
    Account,
  },
  data() {
    return {
      infoVisible: false,
      infoData: {},
      // 遮罩层
      loading: true,
      statusOptions: [
        { label: '待处理', value: '0' },
        { label: '已处理', value: 1 },
      ],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: '',
        phoneNumber: '',
        status: '',
        createDate: '',
        // sortBy: 'create_time',
        // sortType: 'desc',
      },
      list: [],
    }
  },

  created() {
    this.getList()
  },
  methods: {
    previewVideo (src) {
      this.$alert(`<video src="${src}" height="500" autoplay controls disablePictureInPicture></video>`, '预览视频', {
        dangerouslyUseHTMLString: true,
        width: '900px',
        center: true,
        confirmButtonText: '关闭'
      });
    },
    getList() {
      this.loading = true
      const { createDate, ...params } = this.queryParams
      // console.log('createDate----', createDate);
      // return
      const [startTime, endTime] = createDate || []
      cattleHealthList({
        ...params,
        startTime: startTime,
        endTime: endTime ? (endTime + ' 23:59:59') : ''
        // operatingUserId: this.ngjOperatingUserId
        // ...this.queryParams,
      }).then((res) => {
        this.list = res.result.list.map(item => {
          item.picAddrList = item.picAddr?.split(',') || []
          item.videoAddrList = item.videoAddr?.split(',') || []
          return item
        });
        this.total = Number(res.result.total)
      }).finally(() => {
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    openInfo (item) { 
      this.infoData = {...item}
      this.infoVisible = true
    },
    replayItem (item) {
      this.$prompt('', '回复', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder:'请输入诊断信息',
          inputType:'textarea',
          beforeClose: (action, instance, done) => {
            // console.log('instance:', instance);
            if (action === 'confirm') {
              if(!instance.inputValue) {
                this.$message.error('请输入诊断信息')
                return
              }
              cattleHealthReply({
                onlineConsultationId: item.onlineConsultationId,
                diagnosisResult: instance.inputValue
              }).then(res => {
                if(res.code == 200) {
                  this.$message.success('回复成功')
                  this.getList()
                  done()
                }
              })
            } else {
              done()
            }
          }
        }).then(({ value }) => {
          // console.log('value:', value);
        }).catch(() => { 
        });
    },
  },
}
</script>
