<template>
    <div>
        <el-dialog
            title="续签"
            :visible.sync="visibleSync"
            width="1200px"
            :close-on-click-modal="false"
            @close="close"
            append-to-body
        >

        <el-table :data="dataList" border>
                <el-table-column type="index" align="center" width="50" label="序号"></el-table-column>
                <el-table-column label="供应商名称" align="center" prop="shopName" min-width="150"/>
                <el-table-column label="详细地址"  min-width="200" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.shopProvince }}
                        {{ scope.row.shopCity }}
                    </template>
                </el-table-column>
                <el-table-column label="推荐人" align="center" prop="operatingUserName" min-width="150" />
                <el-table-column label="入驻时间" align="center" min-width="200">
                    <template slot-scope="scope">
                        {{ scope.row.entryStartDate }} - {{ scope.row.entryEndDate }}
                    </template>
                </el-table-column>
                <el-table-column label="登记人" align="center" prop="createBy" min-width="140" />
                <el-table-column label="登记时间" align="center" prop="createTime" min-width="160" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="100" fixed="right" >
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="visibleImg(scope.row)" >入驻合同</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
        <el-dialog
            title="入驻合同"
            :visible.sync="visibleSyncImg"
            width="800px"
            :close-on-click-modal="false"
            @close="closeImg"
            append-to-body
        >
            <el-carousel height="500px">
                <el-carousel-item v-for="item in imgList" :key="item">
                    <img :src="item" alt="" srcset="">
                </el-carousel-item>
            </el-carousel>
        </el-dialog>
    </div>
</template>

<script>
import {
    shopMiddlemenHistoryRecord
} from "@/api/xmb/cow_market";
export default {
    data() {
        return {
            dataList: [],
            visibleSync: false,
            imgList: [],
            visibleSyncImg: false
        }
    }, 
    filters: {
        formarStatus: (status) => {
            const statusMap = {
                1: "启用",
                0: "禁用"
            };
            return statusMap[status];
        },
    },
    methods: {
        open(shopId) {
            this.visibleSync = true
            this.getList(shopId)
        },
        getList(shopId) {
            shopMiddlemenHistoryRecord({
                shopId
            }).then(res => {
                this.dataList = res.result
            })
        },
        close() {
            this.visibleSync = false
            this.dataList = []
        },
        visibleImg (row) {
            if (row.entryContract) {
                this.imgList = row.entryContract.split(',') || []
                this.visibleSyncImg = true
            }
        },
        closeImg() {
                this.visibleSyncImg = false
        }
    }
}
</script>

<style>

</style>