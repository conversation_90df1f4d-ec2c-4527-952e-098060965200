<template>
    <div class="app-container">
        <HeadForm :queryParams='queryParams'>
            <el-form-item label="供应商名称" prop="shopName">
            <el-input v-model="queryParams.shopName" placeholder="请输入供应商名称" clearable/>
            </el-form-item>
            <el-form-item label="供应商管理员" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入供应商管理员" clearable/>
            </el-form-item>
            <el-form-item label="推荐人" prop="contactName">
            <el-input v-model="queryParams.contactName" placeholder="请输入推荐人" clearable/>
            </el-form-item>
            <el-form-item label="创建时间" prop="createDate">
                <el-date-picker
                v-model="queryParams.createDate"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="入驻状态" prop="shopStatus">
                <el-select v-model="queryParams.shopStatus" placeholder="请选择入驻状态" >
                    <el-option
                        v-for="item in storeStatus"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
        </HeadForm>
        <el-card shadow="never">
            <el-row class="mb8 form_btn">
                <el-col class="form_btn_col">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handelAdd">新建</el-button>
                </el-col>
            </el-row>
            <el-table v-loading="loading" :data="dataList" border>
                <el-table-column type="index" align="center" width="50" label="序号"></el-table-column>
                <el-table-column label="供应商名称" align="center" prop="shopName" min-width="200"/>
                <el-table-column label="详细地址"  min-width="200" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.shopProvince }}
                        {{ scope.row.shopCity }}
                    </template>
                </el-table-column>
                <el-table-column label="供应商管理员" align="center" prop="managerName" width="180" />
                <el-table-column label="联系电话" align="center" prop="managerPhone" min-width="180" />
                <el-table-column label="企业名称" align="center" prop="userName" min-width="180" />
                <el-table-column label="企业证件号码" align="center" prop="userCertNo" min-width="160" />
                <el-table-column label="推荐人" align="center" prop="operatingUserName" min-width="160" />
                <el-table-column label="入驻时间" align="center" min-width="200">
                    <template slot-scope="scope">
                        {{ scope.row.entryStartDate }} - {{ scope.row.entryEndDate }}
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center" min-width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.shopStatus | formarStatus }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column label="创建人" align="center" prop="createBy" min-width="140" />
                <el-table-column label="创建时间" align="center" prop="createTime" min-width="160" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220" fixed="right" >
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="handleDetail(scope.row)" >查看</el-button>
                        <el-button size="mini" type="text" @click="handleEdit(scope.row)" >编辑</el-button>
                        <el-button size="mini" type="text" @click="renewContract(scope.row)" >续签</el-button>
                        <el-button size="mini" type="text" @click="record(scope.row)" >入驻记录</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <pagination
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        >
        </pagination>
        <el-dialog
        title="续签"
        :visible.sync="visibleSync"
        width="800px"
        :close-on-click-modal="false"
        @close="close"
        append-to-body
        >
            <el-form
                ref="ruleForm"
                label-width="165px"
                class="demo-ruleForm"
                :model="form"
                :rules="rules"
            >

                <el-form-item label="入驻合同：" prop="entryContract">
                    <FileUpload
                        :fileType="['png', 'jpg', 'jpeg']"
                        :fileSize="30"
                        :isShowTip="false"
                        v-model="form.entryContract"
                        :isBackList="true"
                        :limit="5">
                    </FileUpload>
                    <div>
                        可上传<span style="color: red;">5</span>张，
                        大小不超过 <span style="color: red;">5MB</span> 
                        格式为 <span style="color: red;">png/jpg/jpeg</span>
                    </div>
                </el-form-item>
                <el-form-item label="入驻开始时间："  prop="entryStartDate">
                    <el-date-picker
                        v-model="form.entryStartDate"
                        style="width: 100%;"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="入驻结束时间："  prop="entryEndDate">
                    <el-date-picker
                        v-model="form.entryEndDate"
                        style="width: 100%;"
                        type="date"
                        value-format="yyyy-MM-dd"
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">关闭</el-button>
                <el-button type="primary" @click="submit">确定</el-button>
            </span>
        </el-dialog>
        <Record ref="record"></Record>
    </div>
</template>
  
<script>
    import {
        shopMiddlemenPage,
        shopMiddlemenRenewContract
    } from "@/api/xmb/cow_market";
    import Form from "@/views/nlb/components/form.vue";
    import HeadForm from '@/components/HeadForm/index'
    import Record from "./record.vue";
    import {
        tableUi
        } from "@/utils/mixin/tableUi.js";
    export default {
        mixins: [tableUi],
        components: {
            Form,
            HeadForm,
            Record
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                storeStatus: [
                    { label: "禁用", value: "0" },
                    { label: "已启用", value: 1 },
                ],
                dialogAdd:false,
                total: 0,
                showSearch: true,
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    // createDate: [],
                    // shopName: '',
                    // managerName: '',
                    // operatingUserName: '',
                    // shopStatus:''
                },
                dataList: [],
                visibleSync: false,
                form: {
                    shopId:'',
                    entryContract:'',
                    entryStartDate:'',
                    entryEndDate:'',
                },
                rules: {}
            };
        },
        filters: {
            formarStatus: (status) => {
                const statusMap = {
                    1: "启用",
                    0: "禁用"
                };
                return statusMap[status];
            },
        },
        created() {
            const userInfo = JSON.parse(localStorage.getItem('USERINFO'))
        },
        mounted() {
            this.loading = false;
            this.getList()
        },
        methods: {
            getList() {
                const { shopName, managerName, operatingUserName, shopStatus, pageNum, pageSize } = this.queryParams
                const params = {
                    startTime: (this.queryParams?.createDate && this.queryParams?.createDate[0]) || "",
                    endTime: (this.queryParams?.createDate && this.queryParams?.createDate[1]) || "",
                    shopName, managerName, operatingUserName, shopStatus, pageNum, pageSize
                };
                shopMiddlemenPage({
                    ...params,
                }).then((res) => {
                    this.dataList = res.result.list;
                    this.total = Number(res.result.total);
                });
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm("queryForm");
                this.handleQuery();
            },
            handelAdd() {
                this.$router.push({
                    name: 'capitalAccountAddFrom'
                })
            },
            handleDetail(row) {
                this.$router.push({
                    name: 'capitalAccountAddFrom',
                    query: {
                        id: row.shopId,
                        isInfo: 1
                    }
                })
            },
            handleEdit(row) {
                this.$router.push({
                    name: 'capitalAccountAddFrom',
                    query: {
                        id: row.shopId
                    }
                })
            },
            renewContract(row) {
                this.visibleSync = true
                this.form = {
                    shopId:row.shopId,
                    entryContract:row.entryContract,
                    entryStartDate:row.entryStartDate,
                    entryEndDate:row.entryEndDate,
                }
            },
            close() {
                this.visibleSync = false
            },
            submit() {
                shopMiddlemenRenewContract({
                    ...this.form
                }).then(res => {
                    this.getList()
                    this.$message.success('续签成功')
                    this.close()
                })
            },
            record(row) {
                this.$refs.record.open(row.shopId)
            }
        },
    };
</script>
  