<template>
  <div class="app-container">

    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      label-position="left"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="处罚对象">
        <el-select v-model="queryParams.role" placeholder="请选择" value-key='value'>
          <el-option
            v-for="item in roles"
            :key="item.value"
            :label="item.label"

            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处罚类型">
        <el-select v-model="queryParams.type" placeholder="请选择" value-key='value'>
          <el-option
            v-for="item in types"
            :key="item.value"
            :label="item.label"

            :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="corprateName">
        <el-input v-model="queryParams.corprateName" placeholder="请输入姓名/手机号码" clearable />
      </el-form-item>
      <el-form-item label="创建日期">
        <el-date-picker
          v-model="queryParams.fbDateTime1"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>


      <el-form-item >
       <el-button type="primary" size="mini"  @click="handleSearch">查询</el-button>
        <el-button type="primary" size="mini"  @click="resetQuery">重置</el-button>

      </el-form-item>
    </el-form>
    <div style="margin-bottom: 18px;">
      <el-button type="primary" size="mini"  @click="handleAdd" >创建处罚单</el-button>
    </div>

    <el-table v-loading="loading" :data="list" style="width: 100%">
      <el-table-column label="序号" type="index" width="50"> </el-table-column>
      <el-table-column label="处罚对象" prop='lrTime'> </el-table-column>
      <el-table-column label="姓名" prop="provideName"> </el-table-column>
      <el-table-column label="手机号码" prop="typeName" ></el-table-column>
      <el-table-column label="处罚类型" prop="todayJyprice">
        <template slot-scope="scope">
          {{scope.row.todayJyprice}}
        </template>
      </el-table-column>
      <el-table-column label="处罚金额（元）" prop="todaySgprice">
        <template slot-scope="scope">
          {{scope.row.todaySgprice +' 元/斤'}}
        </template>
      </el-table-column>
      <el-table-column label="处罚原因" prop="provideName"> </el-table-column>
      <el-table-column label="操作人" prop="typeName" ></el-table-column>
      <el-table-column label="创建日期" prop='createTime'> </el-table-column>

    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
   <model-form
      :fieldData="fieldData"
      :types='types'
      @close="close"
      v-if="fieldData.open"
      @refresh="refresh"
    ></model-form>
  </div>
</template>

<script>
import {marketIndexPageList,getMarketById,getMarketProvinceList,livestockCategory,livestockVarieties,livestockCategoryTable,insertMarketIndex} from '@/api/xmb/marketIndex/index.js'

import modelForm from './components/modelForm'
export default {

  components: {
  modelForm
  },
  data() {
    return {
      form:{

      },
      roles:[
        {label:'全部',value:''}, {label:'畜牧师',value:'1'}, {label:'合伙人',value:'2'}
      ],
      types:[
        {label:'全部',value:''}, {label:'罚款',value:'1'}, {label:'警告',value:'2'}
      ],
      fieldData: {
        open: false,
        id:''
      },
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        beginDate:'',
        endDate:'',
        lrbeginDate:'',
        lrendDate:'',
        provideId:'',




      },
    };
  },
  async created() {
    this.getList();

  },
  computed: {

  },
  methods: {
    handleSearch(){

        this.queryParams.pageNum=1

      this.getList();
    },

    //刷新页面
    refresh() {
      this.getList();
    },
    /** 查询列表 */
    getList() {
     marketIndexPageList(this.queryParams).then(res=>{
        this.loading=false
        if(res.code==200){
            this.list=res.result.list
            this.total=parseInt(res.result.total)
        }

     })
    },
    /** t添加 */
    handleAdd() {
      this.fieldData.open = true;
      this.fieldData.title = "创建处罚单";
      this.fieldData.id=''
      this.fieldData.disable = false;
    },
    /** 重置按钮操作 */
    resetQuery() {
        this.queryParams.typeValue=''
        this.queryParams.pageNum=1
        this.queryParams={
          pageNum: 1,
          pageSize: 10,
          beginDate:'',
          endDate:'',
          lrbeginDate:'',
          lrendDate:'',
          provideId:'',
          fbDateTime1:'',
          typeValue:[],




        },
        console.log(this.queryParams)
         this.resetForm("queryForm");
        this.getList();
    },

    //关闭弹框
    close() {
      this.fieldData.open = false;
      this.fieldData.disable = false;
      this.fieldData.id=''
    },



  },
};
</script>
<style scoped>
</style>
