<template>
  <div>
    <el-dialog
      :title="fieldData.title"
      :visible.sync="fieldData.open"
      width="500px"
      :close-on-click-modal="true"
      @close="close"
      class="fieldList"
    >

    <el-form ref="form" :model="form" :rules="rules" label-width="80px" label-position="left">
      <el-form-item label="处罚对象" prop="role">
        <el-radio-group v-model="form.role">
          <el-radio label="畜牧师" value="1"></el-radio>
          <el-radio label="合伙人" value="2"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="姓名" prop="userName">
        <el-autocomplete
          v-model="form.userName"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入姓名/手机号码"
          @select="handleSelect"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="处罚类型" prop="type">
        <el-radio-group v-model="form.type">
          <el-radio label="警告" value="1"></el-radio>
          <el-radio label="罚款" value="2"></el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="处罚金额" prop="money">
        <el-input v-model.number="form.money"    placeholder="请输入处罚金额"></el-input>
      </el-form-item>
      <el-form-item label="处罚原因"  prop="remark">
        <el-input v-model="form.remark" maxlength="50" show-word-limit :rule="[{ required: true, message: '请填写处罚原因', trigger: 'blur' }]" :autosize="{ minRows: 2, maxRows: 4}" type="textarea" placeholder="必填，50字以内"></el-input>
      </el-form-item>

      <el-form-item>
         <el-button type="primary"   @click="onSubmit()">确认并提交</el-button >
      </el-form-item>
    </el-form>

    </el-dialog>
  </div>
</template>

<script>

import {marketIndexPageList,getMarketById,getMarketProvinceList,livestockCategory,livestockVarieties,livestockCategoryTable,insertMarketIndex,updateMarketIndex} from '@/api/xmb/marketIndex/index.js'
export default {
  props: {
    fieldData: {
      type: Object,
      default: {},
    },

  },
  data() {
    return {
      form:{
        role:'',
        userName:"",
        money:'',
        remark:'',
      },
      rules: {

        userName: [
          { required: true, message: '请输入姓名/手机号码', trigger: 'change' }
        ],

        money: [

          { required: true, message: '处罚金额不能为空'},
          { type: 'number', message: '处罚金额必须为数字值'}
        ],
        role: [
          { required: true, message: '请选择处罚对象', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择处罚类型', trigger: 'change' }
        ],
        remark: [
          { required: true, message: '请填写处罚原因', trigger: 'blur' }
        ]
      }
    }
  },
 async  created() {

  },

  methods: {
    querySearchAsync(queryString, cb) {
      console.log(queryString)
      let results=[{value:'张三/13772139999',id:1},{value:'李四/13772131111',id:2}]
      this.timeout = setTimeout(() => {
                cb(results);
      }, 3000 * Math.random());
    },
    handleSelect(item) {
      console.log(item);
      console.log(this.form)
    },

    onSubmit(){
      console.log(this.form)
      // return
      if(!this.form.fbDateTime){
        this.$modal.msgError("请选择日期");
        return
      }
      if(!this.form.provideId){
        this.$modal.msgError("请选省份");
        return
      }
      if(this.form.liverstocks.length==0){
        this.$modal.msgError("请录入行情信息");
        return
      }
      for (let i = 0; i < this.form.liverstocks.length; i++) {
        let liverstock=this.form.liverstocks[i]
        if(!liverstock.categoryId){
          this.$modal.msgError("请选活畜种类");

          return
        }

        if(!liverstock.todayJyprice){
          this.$modal.msgError("请输入今日交易价格");
          return
        }

        if(!liverstock.todaySgprice){
          this.$modal.msgError("请输入今日收购价格");
          return
        }
      }

     // this.form.fbDateTime=this.getFbDateTime()
     // this.form.fbDateTime='2022-10-18'
     if(this.data.id){
       let liverstock={
         "indexId":this.data.id,
         "typeId": this.form.liverstocks[0].typeId,
         "typeName":this.form.liverstocks[0].typeName,
         "categoryId": this.form.liverstocks[0].categoryId,
         "categoryName": this.form.liverstocks[0].categoryName,
         "varietiesId": this.form.liverstocks[0].varietiesId,
         "varietiesName": this.form.liverstocks[0].varietiesName,

         "todayJyprice": this.form.liverstocks[0].todayJyprice,
         "todaySgprice": this.form.liverstocks[0].todaySgprice,
         "provideId": this.form.provideId,
         "provideName": this.form.provideName,
       }

       updateMarketIndex(liverstock).then(res=>{

          if(res.code==200){
              this.$modal.msgSuccess("更新成功");
              this.fieldData.open=false
              this.$parent.getList()
          }


       })
     }else{
       insertMarketIndex(this.form).then(res=>{

          if(res.code==200){
              this.$modal.msgSuccess("录入成功");
              this.fieldData.open=false
              this.$parent.getList()
          }


       })
     }

      console.log(this.form)

      // console.log(typeValue)
    },


    close() {
      this.$emit("close");
    },

  },
};
</script>

<style lang="scss">
.fieldList {
  .el-dialog__header {
    background-color: #f4f4f4;
  }
  .el-dialog__footer {
    text-align: center;
  }
  .itemSpan {
    font-weight: 700;
    margin-bottom: 12px;
  }
}
</style>
