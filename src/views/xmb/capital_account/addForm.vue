<template>
    <div>
        <el-dialog
        title="新建资方"
        :visible.sync="visibleSync"
        width="800px"
        :close-on-click-modal="false"
        @close="close"
        append-to-body
        >
            <el-form
                ref="ruleForm"
                label-width="145px"
                class="demo-ruleForm"
                :model="form"
                :rules="rules"
            >
                <el-row class="p_x_30">
                    <el-col :span="24">
                        <el-form-item label="公司名称："  prop="companyName">
                            <el-input v-model="form.companyName" :disabled="isInfo" placeholder="请输入公司名称"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="所在地区：" prop="marketAddressId">
                            <el-cascader
                                :disabled="isInfo"
                                v-model="form.marketAddressId"
                                :options="addressOptions"
                                @change="handleChange"
                                style="width:100%"
                                ref="cascaderAddr"
                            ></el-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="详细地址："  prop="detailAddress">
                            <el-input v-model="form.detailAddress"  :disabled="isInfo" placeholder="请输入详细地址"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="p_x_30">
                    <el-col :span="24">
                        <el-row>
                            <el-col :span="20">
                                <el-form-item label="用户账号："  prop="userId" v-if="isInfo">
                                    <el-select v-model="form.userId" disabled style="width: 100%" >
                                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName" :value="item.userName" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="用户账号："  prop="userId" v-else>
                                    <el-select v-model="form.userId" style="width: 100%" filterable remote reserve-keyword placeholder="输入账号/手机号码搜索" @change="selectUserInfo" :remote-method="searchUserList">
                                        <el-option v-for="item in userList" :key="item.userName" :label="item.userName" :value="item.userId" />
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="4" style="padding-left: 20px;" v-if="!isInfo">
                                <el-popover v-model="popover.visible" placement="left" width="400" trigger="click">
                                    <el-row>
                                        <el-form-item label="用户账号" prop="userName"><el-input v-model="userData.userName" placeholder="请输入用户名称" maxlength="30" /></el-form-item>
                                        <el-form-item label="联系电话" prop="contactNumber"><el-input v-model="userData.contactNumber" placeholder="联系电话" maxlength="11" /></el-form-item>
                                        <el-form-item label="真实姓名" prop="nickName"><el-input v-model="userData.nickName" placeholder="请输入真实姓名" maxlength="60" /></el-form-item>
                                        <el-form-item label="用户密码" prop="password"><el-input v-model="userData.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password auto-complete="new-password"/></el-form-item>
                                    </el-row>
                                    <el-row style="color: red;text-align: right;margin: -10px 0 10px 0">
                                    {{ message.tips }}
                                    </el-row>
                                    <el-row style="text-align: right">
                                        <el-button type="primary" @click="addEnterpriseUser">新 建</el-button>
                                    </el-row>
                                    <el-button type="text" slot="reference">新建</el-button>
                                </el-popover>
                            </el-col>
                        </el-row>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="真实姓名："  prop="managerName">
                            <el-input v-model="form.managerName" disabled placeholder="请输入真实姓名"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="手机号码："  prop="managerPhone">
                            <el-input v-model="form.managerPhone" disabled placeholder="请输入手机号码"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row class="p_x_30">
                    <el-col :span="24">
                        <el-form-item label="推荐人："  prop="operatingUserId" v-if="isInfo">
                            <el-select v-model="form.operatingUserId" disabled style="width: 100%" >
                                <el-option v-for="item in userList1" :key="item.userId" :label="item.userName" :value="item.userId" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="推荐人："  prop="operatingUserId" v-else>
                            <el-select v-model="form.operatingUserId" style="width: 100%" filterable remote reserve-keyword placeholder="输入账号/手机号码搜索" @change="selectUserInfo1" :remote-method="searchUserList1">
                                <el-option v-for="item in userList1" :key="item.userId" :label="item.userName" :value="item.userId" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="开始时间："  prop="entryStartDate">
                            <el-date-picker
                                :disabled="isInfo"
                                v-model="form.entryStartDate"
                                style="width: 100%;"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="结束时间："  prop="entryEndDate">
                            <el-date-picker
                                :disabled="isInfo"
                                v-model="form.entryEndDate"
                                style="width: 100%;"
                                type="date"
                                value-format="yyyy-MM-dd"
                                placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <span  slot="footer" class="dialog-footer" v-if="!isInfo">
                <el-button @click="close">取 消</el-button>
                <el-button type="primary" @click="submit">提 交</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios'
import {
    capitalSideAdd,
    capitalSideUpdate,
    capitalSideInfo
} from "@/api/xmb/cow_market";
import { addUser, getUser, listSimpleUser } from "@/api/system/user";
export default {
    data() {
        return {
            form: {
                "companyName": "", //资方名称
                "managerUserId": "", //企业管理员userid
                "managerLoginName": "", //企业管理员登录账号
                "managerName": "", //企业管理员名称
                "managerPhone": "", //企业管理员联系电话
                "provinceId": "", //企业所在省ID
                "provinceName": "", //企业所在省名称
                "cityId": "", //企业所在市ID
                "cityName": "", //企业所在市名称
                "detailAddress": "", //企业详细地址
                "capitalSideStatus": "", //资方状态 1启用 0禁用
                "auditStatus": "1", //入驻审批状态 1通过 2待确认 3待审批 4 驳回
                "operatingUserId": "", //运营负责人userId
                "operatingUserName": "", //运营负责人名称
                "operatingLoginAccount": "" //运营负责人登录账号
            },
            rules: {
                companyName: [
                    { required: true, message: "请输入供应商名称", trigger: "blur" },
                ],
                marketAddressId: [
                    { required: true, message: "请选择所在地区", trigger: "change" },
                ],
                detailAddress: [
                    { required: true, message: "请输入详细地址", trigger: "blur" },
                ], 
                userId: [
                    { required: true, message: "请选择用户账号", trigger: "change" },
                ], //管理员名称
                operatingUserId: [
                    { required: true, message: "请选择推荐人", trigger: "change" },
                ], //推荐人userID
                entryStartDate: [
                    { required: true, message: "请选择入驻开始时间", trigger: "change" },
                ], //入驻有效日期 开始日期
                entryEndDate: [
                    { required: true, message: "请选择入驻结束时间", trigger: "change" },
                ], //入驻有效日期 结束日期
            },
            addressOptions: [],
            entryContract: [],
            bankCardImage: [],
            userData:{
                userName:"",
                contactNumber:"",
                nickName:"",
                password:""
            },
            userList: [],
            userList1: [],
            popover: {
                visible: false
            },
            message:{
                tips: ""
            },
            shopId: '',
            isInfo: false,
            visibleSync: false
        }
    },
    created() {
        this.getAddressList()
    },
    methods: {
        // 三级地址选择
        handleChange(value) {
            if (value && value.length != 0) {
                let arr = this.$refs['cascaderAddr'].getCheckedNodes()[0].pathLabels;
                this.form.provinceId = arr[0];
                this.form.cityId = arr[1];
                let pathArr = this.$refs['cascaderAddr'].getCheckedNodes()[0].path
                this.form.provinceName = pathArr[0];
                this.form.cityName = pathArr[1];
            }
        },
        // 获取地址信息
        async getAddressList(){
            const {data : res} = await axios.get('/provincialCity.json');
            this.addressOptions = res
        },
        addEnterpriseUser(){
            let userData = this.userData;
            if(!userData.userName){this.message.tips = "请输入用户账号";return false;}
            if(!userData.nickName){this.message.tips = "请输入真实姓名";return false;}
            if(!userData.password){this.message.tips = "请输入登录密码";return false;}
            addUser(userData).then(response => {
                this.userList.push(response.data);
                this.popover.visible = false;
                this.$modal.msgSuccess("新增成功");
            });
        },
        selectUserInfo(data){
            getUser(data).then(response => {
                this.form.managerName = response.data.corprateName || response.data.nickName;
                this.form.managerPhone = response.data.phonenumber;
                // this.form.userId = response.data.userId
                this.form.managerLoginName= response.data.userName
            });
        },
        searchUserList(query){
            let searchParams = { "phonenumber":query };
            listSimpleUser(searchParams).then(response => {
                this.userList = response.data;
            });
        },
        selectUserInfo1(data){
            getUser(data).then(response => {
                this.form.operatingUserName = response.data.corprateName || response.data.nickName;
                this.form.operatingLoginAccount = response.data.phonenumber;
            });
        },
        searchUserList1(query){
            let searchParams = { "phonenumber":query };
            listSimpleUser(searchParams).then(response => {
                this.userList1 = response.data;
            });
        },
        submit() {
            this.$refs.ruleForm.validate((valid) => {
                if (valid) {
                    if (this.shopId) {
                        capitalSideUpdate({
                            ...this.form,
                            shopId: this.shopId
                        }).then(res => {
                            this.$message.success('编辑成功')
                            this.close()
                        })
                    } else {
                        capitalSideAdd({
                            ...this.form
                        }).then(res => {
                            this.$message.success('发布成功')
                            this.$emit('changeList')
                            this.close()
                        })
                    }
                }
            })
        },
        open(id, isInfo) {
            this.visibleSync = true
            if (id) {
                this.shopId = id
                this.getInfo()
            }
            if (isInfo) {
                this.isInfo = true
            } else {
                this.isInfo = false
            }
        },
        close() {
            this.visibleSync = false
            this.form = {
                "companyName": "", //资方名称
                "managerUserId": "", //企业管理员userid
                "managerLoginName": "", //企业管理员登录账号
                "managerName": "", //企业管理员名称
                "managerPhone": "", //企业管理员联系电话
                "provinceId": "", //企业所在省ID
                "provinceName": "", //企业所在省名称
                "cityId": "", //企业所在市ID
                "cityName": "", //企业所在市名称
                "detailAddress": "", //企业详细地址
                "capitalSideStatus": "", //资方状态 1启用 0禁用
                "auditStatus": "1", //入驻审批状态 1通过 2待确认 3待审批 4 驳回
                "operatingUserId": "", //运营负责人userId
                "operatingUserName": "", //运营负责人名称
                "operatingLoginAccount": "" //运营负责人登录账号
            }
        },
        getInfo() {
            capitalSideInfo({
                shopId: this.shopId
            }).then(res => {
                const dataInfo = res.result
                this.searchUserList(dataInfo.managerPhone)
                this.searchUserList1(dataInfo.operatingLoginAccount)
                this.form = {
                    shopName: dataInfo.shopName, //供应商名称
                    provinceId: dataInfo.provinceId, //所在省ID
                    provinceName: dataInfo.provinceName, //所在省
                    cityId: dataInfo.cityId, //所在市ID
                    cityName: dataInfo.cityName, //所在市
                    detailAddress: dataInfo.detailAddress, //详细地址
                    userType: dataInfo.userType, //主体类型 1企业 3个人
                    userName: dataInfo.userName, //主体名称（企业名称、个人姓名）
                    userCertNo: dataInfo.userCertNo, //主体证件号码（企业统一信用代码、个人身份证）
                    contactName: dataInfo.contactName, //业务联系人
                    contactPhone: dataInfo.contactPhone, //业务联系电话
                    openingBank: dataInfo.openingBank, //收款账户开户行
                    bankAccountNo: dataInfo.bankAccountNo, //收款账户号
                    bankAccountName: dataInfo.bankAccountName, //收款账户名称
                    bankCardImage: dataInfo.bankCardImage, //收款银行卡图片
                    managerName: dataInfo.managerName, //管理员名称
                    managerPhone: dataInfo.managerPhone, //管理员手机号
                    managerLoginName: dataInfo.userName, //管理员登录账号
                    operatingUserId: dataInfo.operatingUserId, //推荐人userID
                    operatingUserName: dataInfo.operatingUserName, //推荐人名称
                    operatingLoginAccount: dataInfo.operatingLoginAccount, //推荐人登录账号
                    entryContract: dataInfo.entryContract, //入驻合同
                    entryStartDate: dataInfo.entryStartDate, //入驻有效日期 开始日期
                    entryEndDate: dataInfo.entryEndDate, //入驻有效日期 结束日期
                    shopStatus: '1', //店铺状态 0关闭 1运营中 3所在地区无服务中心
                    auditStatus: '1' //审批状态 1通过 2待完善 3待审批 4驳回
                }
                this.form.marketAddressId = [dataInfo.provinceId, dataInfo.cityId]
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.adopt_title{
    line-height: 48px;
    padding-left: 50px;
    position: relative;
}
.adopt_title::before{
    content: '';
    width: 8px;
    height: 20px;
    position: absolute;
    top: 14px;
    left: 30px;
    background: rgb(86, 114, 250);
}
.p_x_30{
    padding: 0  30px;
}
.footers{
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}
.img_box{
    display: flex;
    img{
        width: 80px;
        margin-right: 15px;
        cursor: pointer;
    }
}
</style>