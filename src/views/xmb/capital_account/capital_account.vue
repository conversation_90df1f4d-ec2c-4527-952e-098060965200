<template>
    <div class="app-container">
        <HeadForm :queryParams='queryParams'>
            <el-form-item label="资方公司" prop="shopName">
            <el-input v-model="queryParams.shopName" placeholder="请输入资方公司" clearable/>
            </el-form-item>
            <el-form-item label="资方管理员" prop="userName">
            <el-input v-model="queryParams.userName" placeholder="请输入资方管理员" clearable/>
            </el-form-item>
            <el-form-item label="启用时间" prop="createDate">
                <el-date-picker
                v-model="queryParams.createDate"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                ></el-date-picker>
            </el-form-item>
            <el-form-item label="状态" prop="shopStatus">
                <el-select v-model="queryParams.shopStatus" placeholder="请选择状态" >
                    <el-option
                        v-for="item in storeStatus"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                    />
                </el-select>
            </el-form-item>
        </HeadForm>
        <el-card shadow="never">
            <el-row class="mb8 form_btn">
                <el-col class="form_btn_col">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handelAdd">新建</el-button>
                </el-col>
            </el-row>
            <el-table v-loading="loading" :data="dataList" border>
                <el-table-column type="index" align="center" width="50" label="序号"></el-table-column>
                <el-table-column label="资方公司" align="center" prop="shopName" min-width="200"/>
                <el-table-column label="详细地址"  min-width="200" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.shopProvince }}
                        {{ scope.row.shopCity }}
                    </template>
                </el-table-column>
                <el-table-column label="资方管理员工" align="center" prop="managerName" width="180" />
                <el-table-column label="企业证件号码" align="center" prop="userCertNo" min-width="160" />
                <el-table-column label="推荐人" align="center" prop="operatingUserName" min-width="160" />
                <el-table-column label="启用时间" align="center" prop="operatingUserName" min-width="160" />
                <el-table-column label="状态" align="center" min-width="120">
                    <template slot-scope="scope">
                        <span>{{ scope.row.shopStatus | formarStatus }}</span
                        >
                    </template>
                </el-table-column>
                <el-table-column label="登记人" align="center" prop="createBy" min-width="140" />
                <el-table-column label="登记时间" align="center" prop="createTime" min-width="160" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220" fixed="right" >
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" @click="handleDetail(scope.row)" >查看</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <pagination
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
        >
        </pagination>
        <AddForm ref="addform"></AddForm>
    </div>
</template>
  
<script>
    import {
        capitalSidePage
    } from "@/api/xmb/cow_market";
    import Form from "@/views/nlb/components/form.vue";
    import HeadForm from '@/components/HeadForm/index'
    import AddForm from "./addForm.vue";
    import {
        tableUi
        } from "@/utils/mixin/tableUi.js";
    export default {
        mixins: [tableUi],
        components: {
            Form,
            HeadForm,
            AddForm
        },
        data() {
            return {
                // 遮罩层
                loading: true,
                storeStatus: [
                    { label: "停用", value: "0" },
                    { label: "启用", value: 1 },
                ],
                dialogAdd:false,
                total: 0,
                showSearch: true,
                queryParams: {
                    pageNum: 1,
                    pageSize: 10,
                    // createDate: [],
                    // shopName: '',
                    // managerName: '',
                    // operatingUserName: '',
                    // shopStatus:''
                },
                dataList: [],
            };
        },
        filters: {
            formarStatus: (status) => {
                const statusMap = {
                    1: "启用",
                    0: "禁用"
                };
                return statusMap[status];
            },
        },
        created() {
            const userInfo = JSON.parse(localStorage.getItem('USERINFO'))
        },
        mounted() {
            this.loading = false;
            this.getList()
        },
        methods: {
            getList() {
                const { companyName, managerName, pageNum, pageSize } = this.queryParams
                const params = {
                    startTime: (this.queryParams?.createDate && this.queryParams?.createDate[0]) || "",
                    endTime: (this.queryParams?.createDate && this.queryParams?.createDate[1]) || "",
                    companyName, managerName, pageNum, pageSize
                };
                capitalSidePage({
                    ...params,
                }).then((res) => {
                    this.dataList = res.result.list;
                    this.total = Number(res.result.total);
                });
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.pageNum = 1;
                this.getList();
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm("queryForm");
                this.handleQuery();
            },
            handelAdd() {
                this.$refs.addform.open()
            },
            handleDetail() {

            }
        },
    };
</script>
  