<template>
  <div class="app-container">
    <HeadForm :queryParams="queryParams">
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input v-model="queryParams.plateNumber" placeholder="请输入车牌号" clearable />
      </el-form-item>
      <el-form-item label="车型" prop="carType">
        <el-select v-model="queryParams.carType" placeholder="请选择车型" clearable>
          <el-option label="大型车" value="大型车" />
          <el-option label="中型车" value="中型车" />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="2" />
        </el-select>
      </el-form-item>
    </HeadForm>

    <el-card shadow="never">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain size="mini" @click="handleAdd">新建</el-button>
          <el-button type="danger" plain size="mini" @click="handleBatchDelete" :disabled="!selectedIds.length">删除</el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="dataList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" align="center" width="55" label="序号" />
        <el-table-column label="车牌号" align="center" prop="plateNumber" min-width="120" />
        <el-table-column label="车型" align="center" prop="carType" min-width="100" />
        <el-table-column label="车长(米)" align="center" prop="carLength" min-width="100" />
        <el-table-column label="车辆行驶证" align="center" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.licenseFrontImage">
              <el-image
                style="width: 30px; height: 30px; cursor: pointer;"
                :src="scope.row.licenseFrontImage"
                :preview-src-list="[scope.row.licenseFrontImage]"
                fit="cover"
              />
            </span>
            <span v-else>未上传</span>
          </template>
        </el-table-column>
        <el-table-column label="车头照片" align="center" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.frontPhoto">
              <el-image
                style="width: 30px; height: 30px; cursor: pointer;"
                :src="scope.row.frontPhoto.split(',')[0]"
                :preview-src-list="scope.row.frontPhoto.split(',')"
                fit="cover"
              />
            </span>
            <span v-else>未上传</span>
          </template>
        </el-table-column>
        <el-table-column label="车尾照片" align="center" min-width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.rearPhoto">
              <el-image
                style="width: 30px; height: 30px; cursor: pointer;"
                :src="scope.row.rearPhoto.split(',')[0]"
                :preview-src-list="scope.row.rearPhoto.split(',')"
                fit="cover"
              />
            </span>
            <span v-else>未上传</span>
          </template>
        </el-table-column>
        <el-table-column label="车辆状态" align="center" min-width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="2"
              active-text="启用"
              inactive-text="禁用"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="车辆归属人" align="center" prop="nickName" min-width="120" />
        <el-table-column label="联系电话" align="center" prop="ownerPhone" min-width="120" />
        <el-table-column label="添加时间" align="center" prop="createTime" min-width="160" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button size="mini" type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="text" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <pagination
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增/编辑弹窗 -->
    <VehicleForm ref="vehicleForm" @refresh="getList" />
  </div>
</template>

<script>
import { carPage, carDelete, carEnable, carDisable } from "@/api/nmb/vehicle";
import HeadForm from '@/components/HeadForm/index';
import { tableUi } from "@/utils/mixin/tableUi.js";
import VehicleForm from './components/vehicleForm.vue';

export default {
  name: "Vehicle",
  mixins: [tableUi],
  components: {
    HeadForm,
    VehicleForm
  },
  data() {
    return {
      loading: false,
      dataList: [],
      total: 0,
      selectedIds: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        plateNumber: '',
        carType: '',
        status: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 重置搜索
    resetQuery() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        plateNumber: '',
        carType: '',
        status: ''
      };
      this.getList();
    },

    // 获取列表数据
    getList() {
      this.loading = true;
      const params = { ...this.queryParams };
      carPage(params).then(res => {
        if (res.code === 200) {
          this.dataList = res.result.list || [];
          this.total = NNumber(res.result.total) || 0;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    // 新增
    handleAdd() {
      this.$refs.vehicleForm.open('add');
    },

    // 编辑
    handleEdit(row) {
      this.$refs.vehicleForm.open('edit', row.id);
    },

    // 删除单个
    handleDelete(row) {
      this.$confirm('请确认是否删除当前车辆?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        carDelete({ carIds: row.id.toString() }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.getList();
          }
        });
      });
    },

    // 批量删除
    handleBatchDelete() {
      if (!this.selectedIds.length) {
        this.$message.warning('请选择要删除的数据');
        return;
      }
      this.$confirm('请确认是否删除选中的车辆?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        carDelete({ carIds: this.selectedIds.join(',') }).then(res => {
          if (res.code === 200) {
            this.$message.success('删除成功');
            this.getList();
          }
        });
      });
    },

    // 状态切换
    handleStatusChange(row) {
      const action = row.status === 1 ? '启用' : '禁用';
      this.$confirm(`请确认是否${action}当前车辆?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const api = row.status === 1 ? carEnable : carDisable;
        api({ id: row.id }).then(res => {
          if (res.code === 200) {
            this.$message.success(`${action}成功`);
            this.getList();
          }
        }).catch(() => {
          // 失败时恢复状态
          row.status = row.status === 1 ? 2 : 1;
        });
      }).catch(() => {
        // 取消时恢复状态
        row.status = row.status === 1 ? 2 : 1;
      });
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id);
    }
  }
};
</script>

<style scoped lang="scss">
.app-container {
  .form_btn {
    margin-bottom: 10px;
  }

  .form_btn_col {
    text-align: left;
  }
}
</style>
