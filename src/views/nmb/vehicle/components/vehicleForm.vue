<template>
  <div>
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false"
      @close="handleClose" class="vehicleDialog" append-to-body>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" class="demo-ruleForm">
        <!-- 车辆信息 -->
        <div class="form-section">
          <div class="section-title">车辆信息</div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input v-model="form.plateNumber" placeholder="请输入车牌号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="车型" prop="carType">
                <el-radio-group v-model="form.carType">
                  <el-radio label="1">大型车</el-radio>
                  <el-radio label="2">中型车</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="车长(米)" prop="carLength">
                <el-input v-model="form.carLength" placeholder="请输入车长" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 车辆行驶证 -->
          <el-form-item label="车辆行驶证" prop="licenseImages">
            <div class="upload-section">
              <div class="upload-item">
                <div class="upload-label">行驶证正面</div>
                <image-upload :disabled="false" v-model="form.licenseFrontImage" :limit="1" :fileSize="2"
                  :isShowTip="true" />
              </div>
              <div class="upload-item">
                <div class="upload-label">行驶证反面</div>
                <image-upload :disabled="false" v-model="form.licenseRearImage" :limit="1" :fileSize="2"
                  :isShowTip="true" />
              </div>
            </div>
          </el-form-item>

          <!-- 车头照片 -->
          <el-form-item label="车头照片">
            <image-upload :disabled="false" v-model="form.frontPhoto" :limit="9" :fileSize="2" :isShowTip="true" />
          </el-form-item>

          <!-- 车尾照片 -->
          <el-form-item label="车尾照片">
            <image-upload :disabled="false" v-model="form.rearPhoto" :limit="9" :fileSize="2" :isShowTip="true" />
          </el-form-item>
        </div>

        <!-- 车辆归属人信息 -->
        <div class="form-section">
          <div class="section-title">车辆归属人信息</div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="车辆归属人" prop="nickName">
                <el-input v-model="form.nickName" placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话" prop="ownerPhone">
                <el-input v-model="form.ownerPhone" placeholder="请输入联系电话" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="身份证号" prop="ownerIdCardNumber">
                <el-input v-model="form.ownerIdCardNumber" placeholder="请输入身份证号" />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 身份证照片 -->
          <el-form-item label="身份证正面">
            <image-upload :disabled="false" v-model="form.ownerIdCardFrontImage" :limit="1" :fileSize="2"
              :isShowTip="true" />
          </el-form-item>

          <el-form-item label="身份证反面">
            <image-upload :disabled="false" v-model="form.ownerIdCardRearImage" :limit="1" :fileSize="2"
              :isShowTip="true" />
          </el-form-item>
        </div>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitForm" :disabled="btnDisabled">
          {{ btnDisabled ? '提交中' : '提交' }}
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { carAdd, carEdit, carInfo } from "@/api/nmb/vehicle";
import { userList } from "@/api/nmb/customer";


export default {
  name: "VehicleForm",
  data() {
    // 手机号验证
    const validatePhone = (rule, value, callback) => {
      if (value && !/^1[3456789]\d{9}$/.test(value)) {
        callback(new Error("请输入正确的手机号"));
      } else {
        callback();
      }
    };

    // 身份证验证
    const validateIdCard = (rule, value, callback) => {
      if (value && !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(value)) {
        callback(new Error("请输入正确的身份证号"));
      } else {
        callback();
      }
    };

    return {
      dialogVisible: false,
      dialogTitle: '',
      mode: 'add', // add | edit
      currentId: null,
      btnDisabled: false,
      form: {
        plateNumber: '',
        carType: '大型车',
        carLength: '',
        licenseFrontImage: '',
        licenseRearImage: '',
        frontPhoto: '',
        rearPhoto: '',
        nickName: '',
        ownerPhone: '',
        ownerIdCardNumber: '',
        ownerIdCardFrontImage: '',
        ownerIdCardRearImage: ''
      },
      rules: {
        plateNumber: [
          { required: true, message: '请输入车牌号', trigger: 'blur' }
        ],
        carType: [
          { required: true, message: '请选择车型', trigger: 'change' }
        ],
        carLength: [
          { required: true, message: '请输入车长', trigger: 'blur' }
        ],
        licenseFrontImage: [
          { required: true, message: '请上传行驶证正面', trigger: 'change' }
        ],
        licenseRearImage: [
          { required: true, message: '请上传行驶证反面', trigger: 'change' }
        ],
        nickName: [
          { required: true, message: '请输入车辆归属人', trigger: 'blur' }
        ],
        ownerPhone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        ownerIdCardNumber: [
          { validator: validateIdCard, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    // 打开弹窗
    open(mode, id = null) {
      this.mode = mode;
      this.currentId = id;
      this.dialogTitle = mode === 'add' ? '新增车辆' : '编辑车辆';
      this.dialogVisible = true;

      if (mode === 'edit' && id) {
        this.getDetail(id);
      } else {
        this.resetForm();
      }
    },

    // 获取详情
    getDetail(id) {
      carInfo({ id }).then(response => {
        if (response.code === 200) {
          this.form = { ...response.result };
        }
      });
    },

    // 提交表单
    submitForm() {
      this.$refs.ruleForm.validate(valid => {
        if (!valid) {
          return;
        }

        this.btnDisabled = true;
        const submitData = { ...this.form };

        if (this.mode === 'edit') {
          submitData.id = this.currentId;
        }

        const api = this.mode === 'add' ? carAdd : carEdit;
        api(submitData).then(response => {
          if (response.code === 200) {
            this.$message.success('保存成功');
            this.$emit('refresh');
            this.handleClose();
          }
          this.btnDisabled = false;
        }).catch(() => {
          this.btnDisabled = false;
        });
      });
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.form = {
        plateNumber: '',
        carType: '大型车',
        carLength: '',
        licenseFrontImage: '',
        licenseRearImage: '',
        frontPhoto: '',
        rearPhoto: '',
        nickName: '',
        ownerPhone: '',
        ownerIdCardNumber: '',
        ownerIdCardFrontImage: '',
        ownerIdCardRearImage: ''
      };
      this.$nextTick(() => {
        this.$refs.ruleForm && this.$refs.ruleForm.clearValidate();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.vehicleDialog {
  .form-section {
    margin-bottom: 20px;

    .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e4e7ed;
    }
  }

  .upload-section {
    display: flex;
    gap: 20px;

    .upload-item {
      flex: 1;

      .upload-label {
        margin-bottom: 8px;
        font-size: 14px;
        color: #606266;
      }
    }
  }

  .dialog-footer {
    text-align: center;
  }
}
</style>
