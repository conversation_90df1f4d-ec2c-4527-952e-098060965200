<template>
    <div class="app-container">
        <HeadForm :queryParams="queryParams" ref="queryForm">
            <el-form-item label="耳标号" prop="livestockManageId">
                <el-input v-model="queryParams.livestockManageId" placeholder="请输入耳标号" clearable size="small"
                    style="width: 200px" />
            </el-form-item>
            <el-form-item label="录入时间" prop="dateRange">
                <el-date-picker v-model="queryParams.dateRange" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width: 240px"
                    value-format="yyyy-MM-dd" />
            </el-form-item>
        </HeadForm>

        <!-- 表格 -->
        <el-card shadow="never">
            <el-table :data="dataList" border v-loading="loading" style="width: 100%" height="400"
                v-tableHeight="{ bottomOffset: 69 }">
                <el-table-column type="index" align="center" label="序号" fixed="left" width="60" />
                <el-table-column prop="earTagNo" label="耳标号" align="center" min-width="170" />
                <el-table-column prop="diseaseType" label="疾病类型" align="center" min-width="100">
                    <template slot-scope="scope">
                        <span>{{ getDiseaseList(scope.row.diseaseType) }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="diseaseSymptoms" label="症状及并发症" align="center" min-width="180" />
                <el-table-column prop="diseaseReason" label="发病原因" align="center" min-width="180" />
                <el-table-column prop="diseaseTreatmentOptions" label="治疗方案" align="center" min-width="180" />
                <el-table-column prop="operatePeopleName" label="兽医名称" align="center" min-width="180" />
                <el-table-column prop="diseaseHealResult" label="治病结果" align="center" min-width="180" />
                <el-table-column prop="operateTime" label="录入时间" align="center" min-width="180" />
            </el-table>
        </el-card>

        <!-- 分页 -->
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import { healingPage } from '@/api/nmb/inventory/index.js'
import HeadForm from '@/components/HeadForm/index'
import { getDicts } from '@/api/system/dict/data'
import { tableUi } from '@/utils/mixin/tableUi.js'

export default {
    mixins: [tableUi],
    components: {
        HeadForm
    },
    data() {
        return {
            // 遮罩层
            loading: true,
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                pastureId: 0,
                penId: 0,
                startTime: '',
                endTime: '',
                operatePeopleName: '',
                pastureName: '',
                livestockManageId: '',
                dateRange: []
            },
            // 表格数据
            dataList: [],
            // 总条数
            total: 0,
            diseaseTypeList: []
        }
    },
    created() {
    },
    mounted() {
        this.getDictData()
        this.loading = false
        this.getList()
    },
    methods: {
        // 获取列表数据
        getList() {
            this.loading = true

            // 处理时间范围
            const params = { ...this.queryParams }
            if (params.dateRange && params.dateRange.length === 2) {
                params.startTime = params.dateRange[0]
                params.endTime = params.dateRange[1]
            }
            delete params.dateRange

            healingPage(params).then(res => {
                if (res.code === 200) {
                    this.dataList = res.result.list || []
                    this.total = Number(res.result.total || 0)
                } else {
                    this.$message.error(res.message || '获取数据失败')
                }
                this.loading = false
            }).catch(error => {
                console.error('获取列表数据失败:', error)
                this.$message.error('获取数据失败')
                this.loading = false
            })
        },

        // 搜索按钮操作
        handleQuery() {
            this.queryParams.pageNum = 1
            this.getList()
        },

        // 重置按钮操作
        resetQuery() {
            // 重置查询参数到初始状态
            this.queryParams = {
                pageNum: 1,
                pageSize: 10,
                pastureId: 0,
                penId: 0,
                startTime: '',
                endTime: '',
                operatePeopleName: '',
                pastureName: '',
                livestockManageId: '',
                dateRange: []
            }
            this.handleQuery()
        },

        // 重置表单
        resetForm(refName) {
            if (this.$refs[refName]) {
                this.$refs[refName].resetFields()
            }
        },
        // 获取疾病类型字典
        async getDictData() {
            try {
                // 获取饲料种类字典
                const diseaseList = await getDicts('livestock_disease_type')
                if (diseaseList.code === 200) {
                    this.diseaseTypeList = diseaseList.data || []
                }
            } catch (error) {
                console.error('获取字典数据失败:', error)
            }
        },
        getDiseaseList(value) {
            const option = this.diseaseTypeList.find(item => parseInt(item.dictValue) === value)
            return option ? option.dictLabel : value
        },
    }
}
</script>

<style scoped lang='scss'></style>
