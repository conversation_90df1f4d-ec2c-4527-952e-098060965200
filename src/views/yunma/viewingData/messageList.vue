<template>
    <div class="message-box">
        <div class="message-list">
            <!-- <div class="message-list-title">系统公告</div> -->
            <div class="message-list-items">
                <div class="message-list-item" v-for="(item, index) in tableData" :key="index" @click="messageInfo(item)">
                    <span>消息</span>
                    <label>{{item.title}}</label>
                </div>
            </div>
        </div>
        <div class="message-list">
            <!-- <div class="message-list-title">操作指南和常见问题</div> -->
            <div class="message-list-items">
                <div class="message-list-item" v-for="(item, index) in tableData2" :key="index" @click="messageInfo(item)">
                    <img src="../../../assets/images/traceability/img_icon.png" alt="">
                    <label>{{item.title}}</label>
                </div>
            </div>
        </div>
        <el-drawer
            class="drawer_box drawer_box1"
            :visible.sync="noticeInfoShow" 
            :show-close="true" 
            :append-to-body="true" 
            :destroy-on-close="true"
            size="80%"
            :title="currentData.title"
            :wrapperClosable="false">
            <Info ref="modelRef" :announcementId="currentData.announcementId"></Info>
        </el-drawer>
    </div>
</template>

<script>
import { announcementPage } from '../../../api/traceability/viewData/index'
import Info from './components/info.vue'
export default {
    data() {
        return {
            tableData: [],
            tableData2: [],
            noticeInfoShow: false,
            currentData: {},
        }
    },
    components: {
        Info
    },
    created() {
        this.getList({
            pageNum: 1,
            pageSize: 4,
        })
        this.getList({
            pageNum: 2,
            pageSize: 4,
        })
    },
    methods: {
        getList(data) {
            announcementPage({
                ...data
            }).then((res) => {
                if (res.code == 200) {
                    if (data.pageNum == 1) {
                        this.tableData = res.result.list
                    } else {
                        this.tableData2 = res.result.list
                    }
                }
            });
        },
        messageInfo(item) {
            this.currentData = item
            this.noticeInfoShow = true;
        }
    }
}
</script>

<style lang="scss" scoped>
.message-box{
    display: flex;
    justify-content: space-between;
    .message-list{
        width: calc(50% - 4px);
        min-height: 120px;
        background: #fff;
        padding: 20px;
        .message-list-title{
            font-size: 16px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #1D2129;
            margin-bottom: 20px;
        }
        .message-list-item{
            display: flex;
            align-items: center;
            font-size: 14px;
            font-family: Source Han Sans CN-Regular, Source Han Sans CN;
            font-weight: 400;
            color: #4E5969;
            margin-bottom: 18px;
            span{
                width: 44px;
                height: 20px;
                background: #CAD3FF;
                font-size: 14px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #5672FA;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: 8px;
                border-radius: 2px 2px 2px 2px;
            }
            img{
                width: 14px;
                height: 14px;
                margin-right: 8px;
            }
            label{
                cursor: pointer;
            }
            &:hover{
                color: #5672FA;
            }
        }
    }
}
</style>
<style lang="scss">

.drawer_box{
    .el-drawer__header{
        background: #F6F6F6;
        margin: 0;
        padding: 18px 20px;
        font-size: 18px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: #1D2129;
        .el-dialog__close{
            font-size: 22px;
            font-weight: 500;
            color: #1D2129;
        }
    }
    .el-drawer__body{
        padding: 0 20px 0 56px;
    }
    .el-button--primary{
        background: #F6F6F6;
    }
    .el-input__inner {
        height: 30px;
    }
    .el-input__icon{
        line-height: normal;
    }
    .el-date-editor .el-range-separator{
        line-height: 20px;
    }
}
.drawer_box_1{
    .el-drawer__body{
        padding: 0;
        .item_box{
            flex: 1;
            .el-form-item{
                margin: 0;
                .el-form-item__content{
                    margin: 0!important;
                }
            }
            .el-input__inner,
            .el-textarea__inner{
                min-width: 320px;
                border: none;
                resize: none;
                background: transparent;
                padding: 0;
            }
        }
        .el-descriptions-item__label,
        .el-descriptions-item__content{
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}
</style>