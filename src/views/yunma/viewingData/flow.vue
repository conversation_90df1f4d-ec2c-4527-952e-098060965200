<template>
    <div class="flow-box">
        <TitleBox title="溯源防伪流程"></TitleBox>
        <div class="flow-list">
            <div class="flow-item">
                <p class="flow-title">1.商家入驻</p>
                <div class="flow-content">
                    <p>小程序申请入驻</p>
                    <p class="txt-blue" @click="goVerify">企业管理核实</p>
                    <p>入驻成功</p>
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">2.开通账号</p>
                <div class="flow-content">
                    <p>账户管理</p>
                    <p>用户授权</p>
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">3.申请用码</p>
                <div class="flow-content">
                    <p class="txt-blue" @click="goCode">溯源码申请</p>
                    <p class="txt-blue" @click="goCode">下载码包</p>
                    <p>委托印刷</p>
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">4.商品贴码</p>
                <div class="flow-content">
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">5.APP商品溯源绑定</p>
                <div class="flow-content">
                    <p>奶源批次</p>
                    <p>按号段扫码绑定或任意扫码绑定</p>
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">6.扫一扫查验</p>
                <div class="flow-content">
                    <p>微信扫描查询</p>
                    <p class="txt-blue" @click="goSecurity">防伪管理</p>
                </div>
            </div>
            <div class="flow-item">
                <p class="flow-title">7.数据生成</p>
                <div class="flow-content">
                    <p class="txt-blue" @click="goProcure">台账管理</p>
                    <p>大数据中心</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TitleBox from './components/titleBox.vue'
export default {
    data() {
        return {

        }
    },
    components: {
        TitleBox
    },
    methods: {
        goSecurity() {
            this.$router.push({
                path: '/yunma/traceability/security'
            })
        },
        goProcure() {
            this.$router.push({
                path: '/yunma/produce/procure'
            })
        },
        goCode() {
            this.$router.push({
                path: '/yunma/traceability/code'
            })
        },
        goVerify() {
            this.$router.push({
                path: '/yunma/basal/verify'
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.flow-box{
    background: #FFFFFF;
    padding: 0 20px;
    .flow-list{
        display: flex;
        padding-top: 25px;
        padding-bottom: 30px;
        .flow-item{
            width: 14.28%;
            font-size: 16px;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #1D2129;
            line-height: 24px;
            p{
                margin: 0;
            }
            .flow-content{
                padding-left: 15px;
                p{
                    font-size: 14px;
                    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                    font-weight: 400;
                    color: #4E5969;
                    line-height: 35px;
                }
            }
        }
        .txt-blue{
            color: #5672FA !important;
            cursor: pointer;
        }
    }
}
</style>