<template>
    <div>
        <div class="trace-tip">
            <p class="trace-title">Hi，欢迎您使用正蓝旗奶食品溯源防伪管理平台 </p>
            <p class="trace-dec">“一物一码”为产品赋能，实现来可追，去可查，提升市场竞争力和奶源保护 </p>
        </div>
        <div class="trace-content">
            <Flow></Flow>
            <DataTotal style="margin-top: 8px;"></DataTotal>
            <MessageList style="margin-top: 8px;"></MessageList>
        </div>
    </div>
</template>

<script>
import Flow from './flow.vue'
import DataTotal from './dataTotal.vue'
import MessageList from './messageList.vue'
export default {
    data() {
        return{
            currenType: '',
        }
    },
    components: {
        Flow,
        DataTotal,
        MessageList
    },
    methods: {
        changeType(value) {
            this.currenType = value
        }
    }
}
</script>

<style lang="scss" scoped>
.trace-tip{
    height: 72px;
    background: #5672FA;
    padding: 12px 24px;
    p{
        margin: 0;
        font-size: 14px;
        font-family: Source Han Sans CN-Bold, Source Han Sans CN;
        font-weight: 700;
        color: #FFFFFF;
        line-height: 24px;
    }
    .trace-dec{
        font-size: 12px;
        opacity: 0.8;
    }
}
.trace-content{
    padding: 10px;
}
</style>