<template>
    <div class="title-box">
        <div class="title_left">
            <span class="title-top">{{title}}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: "demo",
    data() {
        return {};
    },
    props: {
        title: String,
    },
};
</script>

<style scoped lang="scss">
.title-box {
    height: 62px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    font-size: 16px;
    font-family: Source <PERSON>s CN-Medium, Source <PERSON> Sans CN;
    font-weight: 500;
    color: #1D2129;
    border-bottom: 1px solid #F2F3F5;
}
</style>