<template>
    <div class="mt20">
        <div v-html="dataInfo.content"></div>
        <div class="mt20 file_box">
            <div>附件：</div>
            <div v-if="fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'jpeg' || fileSuffix == 'gif'">
                <img width="80" :src="dataInfo.appendixUrl" @click="openFile(dataInfo.appendixUrl)" alt="">
            </div>
            <div class="text_btn" style="cursor: pointer;" v-else @click="openFile(dataInfo.appendixUrl)">查看附件</div>
        </div>
        <el-dialog  width="30%" :modal="false" :visible.sync="dialogImageShow">
            <img width="100%" style="height: 500px;" :src="dataInfo.appendixUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
import { announcementInfo } from '../../../../api/traceability/viewData/index'
export default {
    data() {
        return {
            dataInfo: {},
            dialogImageShow: false,
            fileSuffix: ''
        }
    },
    props: {
        announcementId: String
    },
    watch: {
        announcementId() {
            if (this.announcementId) {
                this.getInfo()
            }
        }
    },
    created() {
        if (this.announcementId) {
            this.getInfo()
        }
    },
    methods: {
        getInfo() {
            announcementInfo({
                announcementId: this.announcementId
            }).then((res) => {
                const data = res.result
                this.dataInfo = data
                this.fileSuffix = this.suffix(data.appendixUrl)
            })
        },
        suffix(strUrl) {
            if(!strUrl) { return }
            return strUrl.substring(strUrl.lastIndexOf('.') + 1)
        },
        openFile(url){
            const fileSuffix = this.suffix(url)
            if (fileSuffix == 'png' || fileSuffix == 'jpg' || fileSuffix == 'gif' || fileSuffix == 'jpeg') {
                this.dialogImageShow = true
            } else {
                window.open(url, '_blank')
            }
        },
    }
}
</script>

<style lang="scss" scoped>
.file_box{
    display: flex;
}
.text_btn,
.text_btn:focus,
.text_btn:hover {
    color: #5672FA;
}
</style>