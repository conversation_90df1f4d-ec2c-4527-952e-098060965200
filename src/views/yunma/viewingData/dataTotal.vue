<template>
    <div class="flow-box" ref="example">
        <TitleBox title="溯源数据总览"></TitleBox>
        <div class="total-list">
            <div class="total-item total-item-border">
                <img src="../../../assets/images/traceability/index_icon_1.png" alt="">
                <div class="total-content">
                    <label>入驻商家数量</label>
                    <span>{{ dataInfo.companyNum }} <i>个</i></span>
                </div>
            </div>
            <div class="total-item" :class="{
                'total-item-border': rowNum != 2
            }">
                <img src="../../../assets/images/traceability/index_icon_2.png" alt="">
                <div class="total-content">
                    <label>奶源总量</label>
                    <span>{{ dataInfo.materialsWeight }} <i>kg</i></span>
                </div>
            </div>
            <div class="total-item" :class="{
                'total-item-border': rowNum != 3
            }">
                <img src="../../../assets/images/traceability/index_icon_3.png" alt="">
                <div class="total-content">
                    <label>溯源商品数量</label>
                    <span>{{ dataInfo.productNum }} <i>个</i></span>
                </div>
            </div>
            <div class="total-item" :class="{
                'total-item-border': rowNum == 3 || rowNum == 5 || rowNum == 6
            }">
                <img src="../../../assets/images/traceability/index_icon_4.png" alt="">
                <div class="total-content">
                    <label>码绑定数量</label>
                    <span>{{ dataInfo.activationNum }} <i>个</i></span>
                </div>
            </div>
            <div class="total-item " :class="{
                'total-item-border': rowNum != 5
            }">
                <img src="../../../assets/images/traceability/index_icon_5.png" alt="">
                <div class="total-content">
                    <label>溯源生码总数量</label>
                    <span>{{ dataInfo.traceCodeNum }} <i>个</i></span>
                </div>
            </div>
            <div class="total-item">
                <img src="../../../assets/images/traceability/index_icon_6.png" alt="">
                <div class="total-content">
                    <label>扫描查验次数</label>
                    <span>{{ dataInfo.scanNum }} <i>次</i></span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TitleBox from './components/titleBox.vue'
import { singleCount } from '../../../api/traceability/viewData/index'
export default {
    data() {
        return {
            rowNum: '',
            dataInfo: {}
        }
    },
    components: {
        TitleBox
    },
    mounted() {
        window.onresize =this.setUi;
        this.$nextTick(()=>{
            this.setUi()
        })
        this.getData()
    },
    methods: {
        getData() {
            singleCount({}).then(res => {
                this.dataInfo = res.result
            })
        },
        setUi(){
            this.$nextTick(()=> {
                this.rowNum = Math.floor(this.$refs.example.offsetWidth / 401)
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.flow-box{
    background: #FFFFFF;
    padding: 0 20px;
    .total-list{
        padding-left: 20px;
        display: flex;
        flex-wrap: wrap;
        padding-top: 63px;
        .total-item{
            width: 284px;
            display: flex;
            align-items: center;
            margin-bottom: 70px;
            margin-right: 117px;
            img{
                width: 54px;
                height: 54px;
            }
            .total-content{
                display: flex;
                flex-direction: column;
                font-size: 12px;
                font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                font-weight: 400;
                color: #1D2129;
                margin-left: 12px;
                span{
                    font-size: 24px;
                    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
                    font-weight: 700;
                    color: #1D2129;
                    i{
                        font-size: 12px;
                        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
                        font-weight: 400;
                        color: #1D2129;
                        font-style: normal;
                    }
                }
            }
        }
        .total-item-border {
            border-right: 1px solid #F2F3F5;
        }
    }
}
</style>