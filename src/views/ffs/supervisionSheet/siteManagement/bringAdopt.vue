<template>
  <div class="app-container">
    <el-card class="mb10 form_box" shadow="never" ref="formBox">
      <el-button  class="mb8" type="success" size="mini" icon="el-icon-arrow-left" @click="goback">返回</el-button>
     <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="140px">
      <el-row class="form_row">
          <el-col class="form_col">
            <el-form-item label="圈舍名称：" prop="earTagNo">
              <el-input v-model="queryParams.earTagNo" placeholder="请输入圈舍名称" clearable />
            </el-form-item>
            <el-form-item label="活畜品种：" prop="varietiesId">
              <el-select v-model="queryParams.varietiesId" clearable filterable>
                <el-option
                  v-for="(item, index) in animalsVarieties"
                  :label="item.varietiesName"
                  :value="item.varietiesId"
                  :key="index"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="活畜类型：" prop="categoryId">
              <el-select v-model="queryParams.categoryId" clearable filterable>
                <el-option
                  v-for="(item, index) in animalsType"
                  :label="item.categoryName"
                  :value="item.categoryId"
                  :key="index"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="销售状态：" prop="status">
              <el-select v-model="queryParams.status" clearable filterable>
                <el-option label="在售" value="2" />
                <el-option label="无" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="margin-left: 140px;">
          <el-col >
            <el-form-item >
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              <template v-if="toggleSearchDom">
                <el-button type="text" @click="packUp" >
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <i
                  :class="{ 'el-icon-arrow-down': !toggleSearchStatus, 'el-icon-arrow-up': toggleSearchStatus }"
                ></i>
              </el-button>
              </template>

            </el-form-item>
          </el-col>
        </el-row>
     </el-form>
    </el-card>
    <el-card shadow="never" class="list_table">
      <el-row class="mb8 form_btn">
        <el-col class="form_btn_col">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addFrom">新增</el-button>
        </el-col>
      </el-row>
      <!-- 表格数据 -->
      <el-table :data="tableData" stripe style="width: 100%" v-loading="loading" :height="tableHeight" border>
        <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
        <el-table-column prop="penName" label="圈舍名称" align="center" />
        <el-table-column prop="penName" label="活畜名称" align="center" >
          <!-- <template slot-scope="scope">{{ scope. }}</template> -->
        </el-table-column>
        <el-table-column prop="penName" label="月龄" align="center" >
          <template slot-scope="scope">{{ handelAge(scope.row.livestockAge, ageData) }}</template>
        </el-table-column>
        <el-table-column prop="penName" label="重量（kg）" align="center" >
          <template slot-scope="scope">{{handelAge(scope.row.livestockWeight, livestockWeight) }}</template>
        </el-table-column>
        <el-table-column prop="penName" label="代养活畜" align="center" />
        <el-table-column prop="penName" label="库存（可认养）" align="center" />
        <el-table-column prop="penName" label="最近更新日期" align="center" />
        <el-table-column label="操作" fixed="right" width="140" align="center">
          <template slot-scope="scope">
            <el-button class="btn_color_t" @click="handelEdit(scope.row.penId)" icon="el-icon-info" size="mini" type="text">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>
<script>
import { selectPasturePenList } from "@/api/ffs/supervisionSheet/siteManagement";
import { getDicts } from "@/api/system/dict/data.js";
import { tableUi } from "@/utils/mixin/tableUi.js";
export default {
  components: {
    addPen,
  },
  mixins: [tableUi],
  data() {
    return {
      dialogAdd: {
        open: false,
        title: "",
      },
      queryParams: {
        gatewayName:'',
        pageNum: 1,
        pageSize: 10,
      },
      loading: true,
      tableData: [],
      ageData: [],
      livestockList: [],
      animalsVarieties: [],
      animalsType: [],
    };
  },
  computed: {
    handelColor (){
      return (value)=>{
        return value==1?'rgb(18, 174, 99)':'#9DA1A8'
      }
    },
    handelAge() {
      return (value, list) => {
        let name = "";
        list.forEach((item) => {
          if (value == item.dictValue) {
            name = item.dictLabel;
          }
        });
        return name;
      };
    },
  },
  created() {
    this.getAgeData()
    this.varietiesList()
    this.getVarieties()
    this.queryParams.pastureId = this.$route.query.id;
    this.getList();
  },

  methods: {
    //列表查询
    getList() {
      selectPasturePenList(this.queryParams).then((res) => {
        if (res.code === 200) {
          this.tableData = res.result;
          this.loading = false;
        }
      });
    },

    //页面返回
    goback() {
      this.$tab.closeOpenPage();
      this.$router.go(-1);
    },
    reset() {
      this.resetForm("queryForm");
    },

    //查看数据字典
    getAgeData() {
      getDicts("livestock_age").then((res) => {
        if (res.code == 200) {
          this.ageData = res.data;
        }
      });
    },
    getWeightData() {
      // 活畜重量
      getDicts("livestock_weight").then((res) => {
          this.livestockList = res.data || []
      })
    },
    getType() {
      varietiesList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsVarieties = res.result;
      });
    },
    getVarieties() {
      animalTypeList({ pageNum: 1, pageSize: 100000 }).then((res) => {
        this.animalsType = res.result;
      });
    },
    //重置
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    //刷新页面
    refreshList() {
      this.getList();
    },
    //搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
  }
};
</script>

<style lang="scss" scoped>
</style>

