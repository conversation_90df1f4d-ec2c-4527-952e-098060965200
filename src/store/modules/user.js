import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import {storeList} from '@/api/xmb/feedMall/shop/createShop'
import { extsysLogin, extsysRegister } from '@/api/nlbao/guarantee'
import {Base64} from 'js-base64'
const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    phonenumber:'',
    user:{},
    store:{},
    userPhone: {},
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_PHONENUMBER:(state,phonenumber)=>{
        state.phonenumber =phonenumber
    },
    SET_USERINFO:(state,user)=>{
        state.user =user
    },
    SET_STOREID:(state,store)=>{
        state.store=store
    },
    SET_USERPHONES: (state, userPhone) => {
      state.userPhone = userPhone
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
     
    //   const password = encryptionValue(userInfo.password)
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid).then(res => {
          setToken(res.token)
          commit('SET_TOKEN', res.token)
          window.localStorage.setItem('showWarning', 1)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    //查询当前登录用户的店铺id
    async getStoreId({ commit, state }){
        await  storeList({phone:state.phonenumber, userId: state.user.userId,
          storeType: 5,}).then(res=>{
            if(res.code==200&&res.result!=null){
                commit('SET_STOREID',res.result)
            }

        })
    },

    // 获取用户信息
    GetInfo({dispatch, commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user;
          window.localStorage.setItem('USERINFO', JSON.stringify(user))
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : user.avatar;
        //   const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.jpg") : this.picPath (user.avatar);
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', avatar)
          commit('SET_PHONENUMBER',user.phonenumber);
          commit('SET_USERINFO',user);
          dispatch('getStoreId')
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 农赢保登录
    /* nlbAoLogin(){
        const userinfo = JSON.parse(window.localStorage.getItem('USERINFO'))
        console.log(userinfo)
        const phone = (userinfo?.enterpriseModel?.companyType === 3)
            ? userinfo.enterpriseModel.adminPhonenumber
            : userinfo.phonenumber;
         const usrParams = {
          extsysAccount: userinfo.userId,
          vendorType: userinfo.vendorType,
          userPhone: phone
        }
        console.log(usrParams)
        // 获取农赢保登录用户信息
        extsysLogin(usrParams).then(res => {
          console.log(res);
          if(res.stautscode === 200) {
            // window.localStorage.setItem('extsysUserInfo',JSON.stringify(res.data))
          } else if(res.stautscode === 2019) {
            this.nlbAoRegister()
          } else {
            this.$message.error(res.msg)
          }
        })
      }, */
    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

//对密码进行加密
 function encryptionValue(password){
    return Base64.encode(password);

}

export default user
